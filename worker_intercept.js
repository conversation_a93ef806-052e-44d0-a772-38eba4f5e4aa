(() => {
  const SPOOFED_CONCURRENCY = 16;
  window.navigator.hardwareConcurrency = 16;
  // Backup original constructors
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;

  // ✅ Intercept URL-based Worker (external scripts)
  window.Worker = function (url, options) {
    // If it's a string URL and NOT a blob: URL
    if (typeof url === "string" && !url.startsWith("blob:")) {
      const injection = `
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => ${SPOOFED_CONCURRENCY}
        });
        importScripts(${JSON.stringify(url)});
      `;
      const blob = new OriginalBlob([injection], {
        type: "application/javascript",
      });
      const spoofedUrl = URL.createObjectURL(blob);
      console.log("🔧 [Worker] Intercepted script URL worker:", url);
      return new OriginalWorker(spoofedUrl, options);
    }

    console.log("🧵 [Worker] Created:", url);
    return new OriginalWorker(url, options);
  };

  // ✅ Intercept Blob constructor (used for blob-based Workers)

  window.Blob = function (parts, options = {}) {
    const type = options.type || "";
    const isJavaScript = type.includes("javascript");

    // Join code parts
    const combined = parts
      .map((p) => (typeof p === "string" ? p : ""))
      .join("");

    if (isJavaScript) {
      // Inject spoofing only in JS-type blobs
      const spoofInjection = `
      try {
        // For worker global scope
        Object.defineProperty(self.navigator, 'hardwareConcurrency', {
          get: () => worker_thread,
          configurable: true
        });
      } catch (e) {}

      try {
        // For window scope fallback (just in case)
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => worker_thread,
          configurable: true
        });
      } catch (e) {}
    `;

      const modifiedParts = [spoofInjection + "\n" + combined];
      return new OriginalBlob(modifiedParts, options);
    }

    // If not JS blob, return original
    return new OriginalBlob(parts, options);
  };
})();
