import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from browserforge.headers import HeaderGenerator
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import AsyncNewContext

def extract_flight_data(html: str, filename="flights_output.json"):
    soup = BeautifulSoup(html, 'html.parser')
    flights_data = []

    flight_containers = soup.find_all('div', {'id': lambda x: x and x.startswith('flight-details-')})

    for container in flight_containers:
        flight_info = {}

        # Extract departure info
        origin = container.find('div', class_='city-code')
        departure_time = container.find('div', class_='flt-times')
        destination = container.find_next('div', class_='city-code')
        if destination and origin and origin == destination:
            destination = destination.find_next('div', class_='city-code')

        flight_info['origin'] = origin.text.strip() if origin else None
        flight_info['departure_time'] = departure_time.text.strip() if departure_time else None
        flight_info['destination'] = destination.text.strip() if destination else None

        # Extract stops info
        stops_tooltip = container.find('span', class_='hidden-accessible')
        if stops_tooltip:
            stops_text = stops_tooltip.text.strip()
            flight_info['stops_details'] = stops_text
            flight_info['stops_count'] = stops_text.split(' ')[2] if len(stops_text.split(' ')) > 2 else "0"

        # Extract connecting legs
        legs = container.find_all('div', class_='connecting-flt-details')
        leg_details = []
        for leg in legs:
            leg_type = leg.get('class')[-1]
            if leg_type == 'cities':
                leg_details.append({'route': leg.text.strip()})
            elif leg_type == 'flight-number':
                if leg_details:
                    leg_details[-1]['flight_number'] = leg.text.strip()
            elif leg_type == 'aircraft-name':
                if leg_details:
                    leg_details[-1]['aircraft'] = leg.text.strip()

        flight_info['legs'] = leg_details

        # Extract amenities
        amenities = container.find('app-amenities-tooltip')
        if amenities:
            flight_info['amenities'] = {
                'wifi': bool(amenities.find(attrs={'title': 'High-speed Wi-Fi'})),
                'power': bool(amenities.find(attrs={'title': 'AC power'}) or amenities.find(attrs={'title': 'USB power'})),
                'streaming': bool(amenities.find(attrs={'title': 'Personal device streaming'}))
            }

        # Extract seat availability
        seats_left = container.find('div', class_='seats-left')
        if seats_left:
            flight_info['seats_available'] = seats_left.text.strip().replace(',', '').split()[0]

        # Extract pricing
        price_button = container.find('button', class_='btn-flight')
        if price_button:
            points_elem = price_button.find('span', class_='amount')
            fee_elem = price_button.find('span', class_='cost')

            flight_info['price'] = {
                'points': points_elem.text.strip() if points_elem else None,
                'extra_fee': fee_elem.text.strip() if fee_elem else None
            }

        flights_data.append(flight_info)

    # 🔽 Write the JSON to a file
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(flights_data, f, indent=4, ensure_ascii=False)

    print(f"✅ Flight data saved to {filename}")

async def main():
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"
    async with  async_playwright() as p:
     browser = await p.chromium.launch( headless=False,slow_mo=1000,
     proxy={"server": proxy_server},
     args=["--ignore-certificate-errors"])
     
     headers = HeaderGenerator(  )
     headers.generate()
    #  print(headers)
     screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    #  ScreenFingerprint= ScreenFingerprint.avail
   
    
     fingerprints = FingerprintGenerator(screen=screen , )
     fingerprint= fingerprints.generate(browser='chrome', os='windows')
     if fingerprint.screen.innerWidth < 1000:
      fingerprint.screen.innerWidth = 1280
     if fingerprint.screen.innerHeight < 600:
      fingerprint.screen.innerHeight = 720
     if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     if fingerprint.screen.clientHeight < 1000:
           fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     fingerprint.pluginsData = {
    "plugins": [
        {
            "name": "Chrome PDF Viewer",
            "description": "Portable Document Format",
            "filename": "internal-pdf-viewer",
            "mimeTypes": [
                {
                    "type": "application/pdf",
                    "suffixes": "pdf",
                    "description": "Portable Document Format",
                    "enabledPlugin": "Chrome PDF Viewer"
                }
            ]
        }
    ],
    "mimeTypes": ["Portable Document Format~~application/pdf~~pdf"]
}
     fingerprint.navigator.userAgentData['platformVersion'] = '10.0.0'
    #  print(fingerprint)
     
     context = await AsyncNewContext(browser, fingerprint= fingerprint)
     

     
     worker_thread= fingerprint.navigator.hardwareConcurrency
     print(f"fingerprint thread {worker_thread}")
     # Step 1: Read the original JS template
     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()

# Step 2: Replace placeholder with dynamic value
      
      modified_content = template_content.replace("_worker_thread", f"{worker_thread}")

# Step 3: Write modified content to new or same file
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
     
     await context.add_init_script(path="./worker_intercept.js")
     page =await context.new_page()
    #  page.on("console", lambda msg: print("PAGE LOG:", msg.text))
  
  
     
     hardware_threads = await page.evaluate("() => navigator.hardwareConcurrency")
     print("Spoofed hardwareConcurrency:", hardware_threads)
     async def handle_worker(worker):
         print("worker created:", worker.url)
         
        #  def on_close():
         worker.on("close", lambda: print("worker destroyed: " + worker.url))
 
        #  worker.on("close", on_close)

    

    #  print(browser.contexts)
     page.set_default_timeout(0)
     await page.goto("https://www.aa.com/booking/search/find-flights")
    
     
    #  element = await page.query_selector("text=One way")
    #  if not element:
     await page.get_by_text("Round trip").click()
     await page.get_by_text("One way").click()
     await page.get_by_text("Redeem Miles").click()
     from_airport = page.locator('#matOriginAirport')
     print(from_airport)
     await from_airport.press_sequentially('LAX',delay=100)
     to_airport= page.locator('#matDestinationAirport')
     await to_airport.press_sequentially('AUH',delay=100)
     departDate= page.locator('#matOneWayDatePicker')
     await departDate.press_sequentially(f"{current_date_selectorUi}",delay=300)
     await page.get_by_role("button",name="Search",exact=True).click()
     await page.wait_for_load_state("domcontentloaded")
     scroll_step = 200

# Get the total scrollable height of the page
     total_height = await page.evaluate("document.body.scrollHeight")

# Calculate number of scrolls needed
     num_steps = total_height // scroll_step

# Perform slow mouse-like scrolling
     for _ in range(num_steps):
      await page.mouse.wheel(0, scroll_step)
      await page.wait_for_timeout(800)  

    #  await page.wait_for_selector(".flight-card", timeout=10000)
    #  await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
     html = await page.content()
     extract_flight_data(html)
    #  test= html.query_selector_all(".flt-times")
    #  print(test)
    #  else:
    #    await page.get_by_text("One way").click()
    #    await page.get_by_text("Redeem Miles").click() 
       
    #    from_airport = page.locator('#reservationFlightSearchForm.originAirport')
    #    await from_airport.press_sequentially('LAX',delay=100)
    #    to_airport= page.locator('#reservationFlightSearchForm.destinationAirport')
    #    await to_airport.press_sequentially('LGA', delay=100)
    #    departDate= page.locator('#aa-leavingOn')
    #    await departDate.press_sequentially(f"{current_date_radioUi}",delay=300)
    #    await page.locator("#bookingModule-submit").click()
     await   page.screenshot(path="test.png")    
     await browser.close()

     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()
      modified_content = template_content.replace(f"{worker_thread}", "_worker_thread")
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
 
asyncio.run(main())
