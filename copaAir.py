import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from enhanced_headless_config import create_stealth_context



async def main():
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    # current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"

    # Use enhanced stealth configuration
    browser, context, playwright, config = await create_stealth_context(
        headless=False,
        proxy_server=None  # Set to proxy_server if you want to use proxy
    )

    try:
        # Add CreepJS-specific bypass script
        await context.add_init_script(path="./creepjs_bypass.js")

        page = await context.new_page()

        # Setup page for API access with realistic headers
        await config.setup_page_for_api_access(page)

        # Add random delays to simulate human behavior
        await config.add_random_delays(page, 2000, 4000)

        print("🌐 Testing CreepJS detection...")

        # Test CreepJS detection
        await page.goto("https://abrahamjuliot.github.io/creepjs/")
        await page.wait_for_load_state("domcontentloaded")

        # Wait for initial page load
        await config.add_random_delays(page, 5000, 8000)

        # Simulate human behavior
        await config.simulate_human_behavior(page)

        # Wait for all scripts to execute
        await config.add_random_delays(page, 5000, 10000)

        # Check for API access denied error
        try:
            error_element = await page.query_selector('text=400')
            if error_element:
                print("⚠️ 400 error detected - trying alternative approach...")

                # Try refreshing with different approach
                await page.reload()
                await config.add_random_delays(page, 3000, 5000)

        except Exception as e:
            print(f"ℹ️ No 400 error found: {e}")

        # Take screenshot for analysis
        await page.screenshot(path="creepjs_test.png", full_page=True)
        print("📸 CreepJS test screenshot saved: creepjs_test.png")

        # Get page content for analysis
        content = await page.content()

        # Check for specific detection indicators
        if "400" in content:
            print("❌ API access denied detected")
        elif "headless" in content.lower():
            print("❌ Headless detection found")
        elif "bot" in content.lower():
            print("❌ Bot detection found")
        else:
            print("✅ No obvious detection found")

        print("🔍 CreepJS test completed")

    finally:
        await browser.close()
        await playwright.stop()
 
asyncio.run(main())
