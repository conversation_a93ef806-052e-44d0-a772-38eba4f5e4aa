import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from enhanced_headless_config import create_stealth_context



async def main():
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    # current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"

    # Use enhanced stealth configuration
    browser, context, playwright, config = await create_stealth_context(
        headless=True,
        proxy_server=None  # Set to proxy_server if you want to use proxy
    )

    try:
        # Add enhanced stealth script
        await context.add_init_script(path="./enhanced_stealth_script.js")

        page = await context.new_page()

        # Add random delays to simulate human behavior
        await config.add_random_delays(page, 2000, 4000)

        # Test headless detection
        await page.goto("https://abrahamjuliot.github.io/creepjs/")
        await page.wait_for_load_state("domcontentloaded")

        # Simulate human behavior
        await config.simulate_human_behavior(page)

        # Wait for page to fully load
        await config.add_random_delays(page, 3000, 5000)

        # Get page content and take screenshot
        report = await page.content()
        print("Enhanced headless detection test completed")
        await page.screenshot(path="enhanced_stealth_test.png")

    finally:
        await browser.close()
        await playwright.stop()
 
asyncio.run(main())
