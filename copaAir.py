import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from browserforge.headers import Header<PERSON>enerator
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import AsyncNewContext



async def main():
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    # current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"
    async with  async_playwright() as p:
     browser = await p.chromium.launch(headless=True,slow_mo=1000)
    #  proxy={"server": proxy_server},
    #  args=["--ignore-certificate-errors"])
     
     headers = HeaderGenerator(  )
     headers.generate()
    #  print(headers)
     screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    #  ScreenFingerprint= ScreenFingerprint.avail
   
    
     fingerprints = FingerprintGenerator(screen=screen , )
     fingerprint= fingerprints.generate(browser='chrome', os='windows')
     if fingerprint.screen.innerWidth < 1000:
      fingerprint.screen.innerWidth = 1280
     if fingerprint.screen.innerHeight < 600:
      fingerprint.screen.innerHeight = 720
     if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     if fingerprint.screen.clientHeight < 1000:
           fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     fingerprint.pluginsData = {
    "plugins": [
        {
            "name": "Chrome PDF Viewer",
            "description": "Portable Document Format",
            "filename": "internal-pdf-viewer",
            "mimeTypes": [
                {
                    "type": "application/pdf",
                    "suffixes": "pdf",
                    "description": "Portable Document Format",
                    "enabledPlugin": "Chrome PDF Viewer"
                }
            ]
        }
    ],
    "mimeTypes": ["Portable Document Format~~application/pdf~~pdf"]
}
     fingerprint.navigator.userAgentData['platformVersion'] = '10.0.0'
    #  print(fingerprint)
     
     context = await AsyncNewContext(browser, fingerprint= fingerprint)
     

     
#      worker_thread= fingerprint.navigator.hardwareConcurrency
#      print(f"fingerprint thread {worker_thread}")
#      # Step 1: Read the original JS template
#      with open("worker_intercept.js", "r", encoding="utf-8") as f:
#       template_content = f.read()

# # Step 2: Replace placeholder with dynamic value
      
#       modified_content = template_content.replace("_worker_thread", f"{worker_thread}")

# # Step 3: Write modified content to new or same file
#       with open("worker_intercept.js", "w", encoding="utf-8") as f:
#        f.write(modified_content)
#        print("File updated successfully.")
     
#      await context.add_init_script(path="./worker_intercept.js")
     page =await context.new_page()
     await page.goto("https://infosimples.github.io/detect-headless/")
     report = await page.content()
     print(report)
     await   page.screenshot(path="test.png")    
     await browser.close()

    #  with open("worker_intercept.js", "r", encoding="utf-8") as f:
    #   template_content = f.read()
    #   modified_content = template_content.replace(f"{worker_thread}", "_worker_thread")
    #   with open("worker_intercept.js", "w", encoding="utf-8") as f:
    #    f.write(modified_content)
    #    print("File updated successfully.")
 
asyncio.run(main())
