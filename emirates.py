import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from browserforge.headers import Header<PERSON>enerator
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import AsyncNewContext



async def main():
    
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"
    async with  async_playwright() as p:
     browser = await p.chromium.launch( headless=False,slow_mo=1000)
    #  proxy={"server": proxy_server},
    #  args=["--ignore-certificate-errors"])
     
     headers = HeaderGenerator(  )
     headers.generate()
    #  print(headers)
     screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    #  ScreenFingerprint= ScreenFingerprint.avail
   
    
     fingerprints = FingerprintGenerator(screen=screen , )
     fingerprint= fingerprints.generate(browser='chrome', os='windows')
     if fingerprint.screen.innerWidth < 1000:
      fingerprint.screen.innerWidth = 1280
     if fingerprint.screen.innerHeight < 600:
      fingerprint.screen.innerHeight = 720
     if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     if fingerprint.screen.clientHeight < 1000:
           fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     fingerprint.pluginsData = {
    "plugins": [
        {
            "name": "Chrome PDF Viewer",
            "description": "Portable Document Format",
            "filename": "internal-pdf-viewer",
            "mimeTypes": [
                {
                    "type": "application/pdf",
                    "suffixes": "pdf",
                    "description": "Portable Document Format",
                    "enabledPlugin": "Chrome PDF Viewer"
                }
            ]
        }
    ],
    "mimeTypes": ["Portable Document Format~~application/pdf~~pdf"]
}
     fingerprint.navigator.userAgentData['platformVersion'] = '10.0.0'
    #  print(fingerprint)
     
     context = await AsyncNewContext(browser, fingerprint= fingerprint)
     

     
     worker_thread= fingerprint.navigator.hardwareConcurrency
     print(f"fingerprint thread {worker_thread}")
     # Step 1: Read the original JS template
     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()

# Step 2: Replace placeholder with dynamic value
      
      modified_content = template_content.replace("_worker_thread", f"{worker_thread}")

# Step 3: Write modified content to new or same file
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
     
     await context.add_init_script(path="./worker_intercept.js")
     page =await context.new_page()
    #  page.on("console", lambda msg: print("PAGE LOG:", msg.text))
  
  
     
     hardware_threads = await page.evaluate("() => navigator.hardwareConcurrency")
     print("Spoofed hardwareConcurrency:", hardware_threads)
     async def handle_worker(worker):
         print("worker created:", worker.url)
         
        #  def on_close():
         worker.on("close", lambda: print("worker destroyed: " + worker.url))
 
        #  worker.on("close", on_close)

    

    #  print(browser.contexts)
     page.set_default_timeout(0)
     await page.goto("https://www.emirates.com/in/english/book/#")
     from_ = [
    "DXB",  # Dubai International (Hub)
    "JFK",  # New York - John F. Kennedy
    "LAX",  # Los Angeles
    "LHR",  # London Heathrow
    "SYD",  # Sydney
    "BOM",  # Mumbai
    "FRA",  # Frankfurt
    "SIN",  # Singapore
    "YYZ",  # Toronto Pearson
    "GRU"   # São Paulo Guarulhos
]

     to = [
    "LHR",  # London Heathrow
    "DXB",  # Dubai (hub for return)
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB",  # Dubai
    "DXB"   # Dubai
]


    #  await page.locator('').click()
    #  test= t page.locator('#shopWithMiles').is_visible
    #  print(test)
     await page.locator("#onetrust-accept-btn-handler").click()
     await page.get_by_text("One way").click()
    #  await page.locator("#date-input0").click(force=True)
    #  await page.wait_for_timeout(2000)
    #  await page.locator('td[id="28-06-2025"]').click(timeout=2000)
    #  await page.locator('.CalendarMonth_table.CalendarMonth_table_1').filter(has=page.locator('td[id="28-06-2025"]')).click(timeout=2000)
    #  target_day = test1.locator('td#28-06-2025')
    #  await target_day.click()
     await page.wait_for_timeout(1000)
    #  page.get_by_role("listitem").filter(has_text="One way").click(timeout=2000)
    #  print(test)
    #  await page.locator("#ui-list-selectTripType1").click()
    #  await page.locator('#shopWithMiles').click(force=True)
    #  await page.locator('#input_departureDate_1').click(timeout=3000)
    #  await page.locator(f"""[aria-label="27 June 2025, Friday"]""").click()
    #  await page.locator(f"""[aria-label="done"]""").click()




    #  print (test)
     for x in range(10):
        
        from_airport =  page.locator('[class="input-field__input ellipsis"]').nth(0)
        print(from_airport)
        await from_airport.click()
        await from_airport.fill("")
       
        # searchInput=page.locator("#search_input")
        
        await from_airport.press_sequentially(f'{from_[x]}',delay=100)
        await page.wait_for_timeout(2000)
        await from_airport.press("Enter")

        to_airport =   page.locator('[class="input-field__input ellipsis"]').nth(1)
        print(from_airport)
        # await to_airport.click()
        await to_airport.fill("")
       
        # searchInput=page.locator("#search_input")
        
        await to_airport.press_sequentially(f'{to[x]}',delay=100)
        await page.wait_for_timeout(2000)
        await to_airport.press("Enter")
        await page.locator('td[id="28-06-2025"]').click(timeout=2000)
        await page.get_by_text("Search flights").nth(0).click()
        # value= await departDate.input_value()
        # if(not value):
        #  await departDate.press_sequentially(f"{current_date_selectorUi}",delay=300)
    #  await   page.screenshot(path="partialbeforeSearch.png") 
        # await context.tracing.start(screenshots=True, snapshots=True, sources=True)
        # await page.locator("#findFlights").nth(0).click()
        await page.wait_for_load_state("domcontentloaded")
        await page.wait_for_timeout(10000)
        # await page.locator('div.grid-item--table:has(.calendar-cell)').nth(0).click()

       
        await page.wait_for_selector(".grid-cell.grid-item.grid-item--table")
        available_date=await page.locator(".grid-cell.grid-item.grid-item--table").filter(has=page.locator('.calendar-cell')).nth(0).click(timeout=2000)
        
        
        # await page.locator(f"""[class="flexible-dates-grid-row-cell isPriceCell cursorPointer isSelected isInRange ng-star-inserted"] [tabindex="0"]""").click(timeout=2000)
        await page.get_by_role("button",name="Continue").click() 
        await page.wait_for_timeout(10000)
        await page.screenshot(path="partialbeforeSearch.png")
       
        moreResult = page.get_by_text(" See More Results ")
        test = await page.get_by_text(" See More Results ").is_visible()
        print(test)
        while(await moreResult.is_visible()):
            if await moreResult.is_visible(): 
             await moreResult.evaluate("el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })")
             await moreResult.click()
            #  await page.get_by_text(" See More Results ").click()
            moreResult = page.get_by_text(" See More Results ")
            print(moreResult)# Step 1: locator
            #  await scrollTillFeverResult.wait_for(timeout=5000)              # Step 2: wait for it
            #  await scrollTillFeverResult.evaluate("el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })")  # Step 3: scroll
        await page.goto("https://www.delta.com/flightsearch/book-a-flight")
    #  scroll_step = 200

# # Get the total scrollable height of the page
#      total_height = await page.evaluate("document.body.scrollHeight")

# # Calculate number of scrolls needed
#      num_steps = total_height // scroll_step


    #  for _ in range(num_steps):
    #   await page.mouse.wheel(0, scroll_step)
    #   await page.wait_for_timeout(800)  

    #  html = await page.content()
     

     await   page.screenshot(path="test.png")    
     await browser.close()

     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()
      modified_content = template_content.replace(f"{worker_thread}", "_worker_thread")
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
 
asyncio.run(main())
