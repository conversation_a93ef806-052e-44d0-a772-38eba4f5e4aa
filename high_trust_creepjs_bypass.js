(() => {
  'use strict';

  // High Trust Score CreepJS Bypass
  // Designed to achieve maximum trust score on CreepJS
  
  console.log('🛡️ Loading High Trust CreepJS Bypass...');

  // Get dynamic values from window (set by Python)
  const DYNAMIC_HARDWARE_CONCURRENCY = window.BROWSERFORGE_HARDWARE_CONCURRENCY || 8;
  const DYNAMIC_USER_AGENT = window.BROWSERFORGE_USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  const DYNAMIC_PLATFORM = window.BROWSERFORGE_PLATFORM || 'Win32';
  
  console.log('📊 Using dynamic values:', {
    hardwareConcurrency: DYNAMIC_HARDWARE_CONCURRENCY,
    userAgent: DYNAMIC_USER_AGENT.substring(0, 50) + '...',
    platform: DYNAMIC_PLATFORM
  });

  // Store original constructors before any modifications
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;
  const OriginalURL = window.URL;

  // 1. WebDriver Detection Bypass (Comprehensive)
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
    configurable: true
  });

  // Remove all webdriver traces
  delete window.webdriver;
  delete window.document.webdriver;
  delete window.navigator.webdriver;

  // Override getAttribute for webdriver detection
  const originalGetAttribute = Element.prototype.getAttribute;
  Element.prototype.getAttribute = function(name) {
    if (name === 'webdriver') {
      return null;
    }
    return originalGetAttribute.call(this, name);
  };

  // 2. Hardware Concurrency (Dynamic)
  Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => DYNAMIC_HARDWARE_CONCURRENCY,
    configurable: true
  });

  // 3. User Agent (Dynamic)
  Object.defineProperty(navigator, 'userAgent', {
    get: () => DYNAMIC_USER_AGENT,
    configurable: true
  });

  // 4. Platform (Dynamic)
  Object.defineProperty(navigator, 'platform', {
    get: () => DYNAMIC_PLATFORM,
    configurable: true
  });

  // 5. Enhanced Chrome Object for High Trust
  if (!window.chrome || Object.keys(window.chrome).length === 0) {
    Object.defineProperty(window, 'chrome', {
      get: () => ({
        runtime: {
          onConnect: undefined,
          onMessage: undefined,
          PlatformOs: {
            MAC: "mac",
            WIN: "win",
            ANDROID: "android",
            CROS: "cros",
            LINUX: "linux",
            OPENBSD: "openbsd"
          },
          PlatformArch: {
            ARM: "arm",
            X86_32: "x86-32",
            X86_64: "x86-64"
          }
        },
        csi: () => ({}),
        loadTimes: () => ({
          requestTime: performance.timeOrigin / 1000,
          startLoadTime: performance.timeOrigin / 1000,
          commitLoadTime: performance.timeOrigin / 1000,
          finishDocumentLoadTime: performance.timeOrigin / 1000,
          finishLoadTime: performance.timeOrigin / 1000,
          firstPaintTime: performance.timeOrigin / 1000,
          firstPaintAfterLoadTime: 0,
          navigationType: "Other"
        }),
        app: {
          isInstalled: false,
          InstallState: {
            DISABLED: "disabled",
            INSTALLED: "installed",
            NOT_INSTALLED: "not_installed"
          },
          RunningState: {
            CANNOT_RUN: "cannot_run",
            READY_TO_RUN: "ready_to_run",
            RUNNING: "running"
          }
        }
      }),
      configurable: true
    });
  }

  // 6. Enhanced Plugin Configuration for High Trust
  const realisticPlugins = [
    {
      name: "Chrome PDF Plugin",
      description: "Portable Document Format",
      filename: "internal-pdf-viewer",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
        enabledPlugin: "Chrome PDF Plugin"
      }
    },
    {
      name: "Chrome PDF Viewer",
      description: "Portable Document Format",
      filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
        enabledPlugin: "Chrome PDF Viewer"
      }
    },
    {
      name: "Native Client",
      description: "Native Client",
      filename: "internal-nacl-plugin",
      length: 2,
      0: {
        type: "application/x-nacl",
        suffixes: "",
        description: "Native Client Executable",
        enabledPlugin: "Native Client"
      },
      1: {
        type: "application/x-pnacl",
        suffixes: "",
        description: "Portable Native Client Executable",
        enabledPlugin: "Native Client"
      }
    }
  ];

  Object.defineProperty(navigator, 'plugins', {
    get: () => realisticPlugins,
    configurable: true
  });

  // 7. MIME Types
  const realisticMimeTypes = {
    length: 4,
    0: { type: "application/pdf", suffixes: "pdf", description: "Portable Document Format" },
    1: { type: "application/x-nacl", suffixes: "", description: "Native Client Executable" },
    2: { type: "application/x-pnacl", suffixes: "", description: "Portable Native Client Executable" },
    3: { type: "text/plain", suffixes: "txt", description: "Plain Text" }
  };

  Object.defineProperty(navigator, 'mimeTypes', {
    get: () => realisticMimeTypes,
    configurable: true
  });

  // 8. Language Configuration
  Object.defineProperty(navigator, 'language', {
    get: () => 'en-US',
    configurable: true
  });

  Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
    configurable: true
  });

  // 9. Screen Consistency for High Trust
  Object.defineProperty(screen, 'availWidth', {
    get: () => window.innerWidth,
    configurable: true
  });

  Object.defineProperty(screen, 'availHeight', {
    get: () => window.innerHeight,
    configurable: true
  });

  // 10. Comprehensive Worker Interception
  console.log('🔧 Setting up comprehensive worker interception for high trust...');
  
  window.Worker = function(scriptURL, options) {
    console.log('🔧 [Worker] Intercepting:', scriptURL);
    
    if (typeof scriptURL === 'string') {
      const injectionScript = `
        console.log('🛡️ High trust worker spoofing active');
        
        // Comprehensive navigator spoofing in worker
        Object.defineProperty(self.navigator, 'hardwareConcurrency', {
          get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
          configurable: true
        });
        
        Object.defineProperty(self.navigator, 'userAgent', {
          get: () => '${DYNAMIC_USER_AGENT}',
          configurable: true
        });
        
        Object.defineProperty(self.navigator, 'platform', {
          get: () => '${DYNAMIC_PLATFORM}',
          configurable: true
        });
        
        Object.defineProperty(self.navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });
        
        Object.defineProperty(self.navigator, 'language', {
          get: () => 'en-US',
          configurable: true
        });
        
        Object.defineProperty(self.navigator, 'languages', {
          get: () => ['en-US', 'en'],
          configurable: true
        });
        
        // Remove automation indicators
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        
        console.log('🛡️ Worker spoofing complete, hardwareConcurrency:', ${DYNAMIC_HARDWARE_CONCURRENCY});
        
        // Import original script
        try {
          importScripts('${scriptURL}');
        } catch (e) {
          console.error('Worker import failed:', e);
        }
      `;
      
      const blob = new OriginalBlob([injectionScript], {
        type: 'application/javascript'
      });
      
      const blobURL = OriginalURL.createObjectURL(blob);
      return new OriginalWorker(blobURL, options);
    }
    
    return new OriginalWorker(scriptURL, options);
  };

  // 11. Enhanced Blob Interception
  window.Blob = function(parts, options = {}) {
    const type = options.type || '';
    
    if (type.includes('javascript') || type.includes('application/javascript')) {
      const originalContent = parts.map(part => 
        typeof part === 'string' ? part : ''
      ).join('');
      
      const spoofingInjection = `
        if (typeof self !== 'undefined') {
          Object.defineProperty(self.navigator, 'hardwareConcurrency', {
            get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
            configurable: true
          });
          
          Object.defineProperty(self.navigator, 'userAgent', {
            get: () => '${DYNAMIC_USER_AGENT}',
            configurable: true
          });
          
          Object.defineProperty(self.navigator, 'platform', {
            get: () => '${DYNAMIC_PLATFORM}',
            configurable: true
          });
          
          Object.defineProperty(self.navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
          });
        }
      `;
      
      const modifiedParts = [spoofingInjection + '\n' + originalContent];
      return new OriginalBlob(modifiedParts, options);
    }
    
    return new OriginalBlob(parts, options);
  };

  // 12. Remove Automation Indicators
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

  // 13. Permissions API for High Trust
  const originalPermissionsQuery = navigator.permissions.query;
  navigator.permissions.query = (parameters) => {
    const permission = parameters.name;
    if (permission === 'notifications') {
      return Promise.resolve({ state: 'granted' });
    }
    return originalPermissionsQuery.call(navigator.permissions, parameters);
  };

  console.log('✅ High Trust CreepJS bypass loaded successfully');
  console.log('📊 Final values:', {
    hardwareConcurrency: navigator.hardwareConcurrency,
    userAgent: navigator.userAgent.substring(0, 50) + '...',
    platform: navigator.platform,
    webdriver: navigator.webdriver
  });
})();
