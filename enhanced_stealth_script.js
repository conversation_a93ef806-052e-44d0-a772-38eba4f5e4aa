(() => {
  'use strict';

  // Enhanced stealth script for comprehensive headless detection bypass
  
  // 1. Hardware Concurrency Spoofing
  const SPOOFED_CONCURRENCY = Math.floor(Math.random() * 8) + 4; // 4-12 cores
  
  // Override navigator.hardwareConcurrency
  Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => SPOOFED_CONCURRENCY,
    configurable: true
  });

  // 2. WebDriver Detection Bypass
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
    configurable: true
  });

  // 3. Chrome Runtime Detection Bypass
  if (window.chrome) {
    // Make chrome object more realistic
    Object.defineProperty(window, 'chrome', {
      get: () => ({
        runtime: {
          onConnect: undefined,
          onMessage: undefined,
          PlatformOs: {
            MAC: "mac",
            WIN: "win",
            ANDROID: "android",
            CROS: "cros",
            LINUX: "linux",
            OPENBSD: "openbsd"
          },
          PlatformArch: {
            ARM: "arm",
            X86_32: "x86-32",
            X86_64: "x86-64"
          },
          PlatformNaclArch: {
            ARM: "arm",
            X86_32: "x86-32",
            X86_64: "x86-64"
          }
        },
        csi: () => {},
        loadTimes: () => ({
          requestTime: Date.now() / 1000 - Math.random() * 10,
          startLoadTime: Date.now() / 1000 - Math.random() * 5,
          commitLoadTime: Date.now() / 1000 - Math.random() * 3,
          finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 2,
          finishLoadTime: Date.now() / 1000 - Math.random(),
          firstPaintTime: Date.now() / 1000 - Math.random(),
          firstPaintAfterLoadTime: 0,
          navigationType: "Other"
        }),
        app: {
          isInstalled: false,
          InstallState: {
            DISABLED: "disabled",
            INSTALLED: "installed",
            NOT_INSTALLED: "not_installed"
          },
          RunningState: {
            CANNOT_RUN: "cannot_run",
            READY_TO_RUN: "ready_to_run",
            RUNNING: "running"
          }
        }
      }),
      configurable: true
    });
  }

  // 4. Permissions API Spoofing
  const originalQuery = window.navigator.permissions.query;
  window.navigator.permissions.query = (parameters) => {
    return parameters.name === 'notifications' 
      ? Promise.resolve({ state: Notification.permission })
      : originalQuery(parameters);
  };

  // 5. Plugin and MIME Type Spoofing
  const mockPlugins = [
    {
      name: "Chrome PDF Viewer",
      description: "Portable Document Format",
      filename: "internal-pdf-viewer",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
        enabledPlugin: "Chrome PDF Viewer"
      }
    },
    {
      name: "Chromium PDF Viewer", 
      description: "Portable Document Format",
      filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf", 
        description: "Portable Document Format",
        enabledPlugin: "Chromium PDF Viewer"
      }
    },
    {
      name: "Microsoft Edge PDF Viewer",
      description: "Portable Document Format", 
      filename: "pdf",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
        enabledPlugin: "Microsoft Edge PDF Viewer"
      }
    }
  ];

  Object.defineProperty(navigator, 'plugins', {
    get: () => mockPlugins,
    configurable: true
  });

  // 6. Language Spoofing
  Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
    configurable: true
  });

  // 7. Platform Spoofing
  Object.defineProperty(navigator, 'platform', {
    get: () => 'Win32',
    configurable: true
  });

  // 8. Battery API Spoofing
  if ('getBattery' in navigator) {
    const originalGetBattery = navigator.getBattery;
    navigator.getBattery = () => {
      return Promise.resolve({
        charging: true,
        chargingTime: 0,
        dischargingTime: Infinity,
        level: 0.8 + Math.random() * 0.2,
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => {}
      });
    };
  }

  // 9. Connection API Spoofing
  if ('connection' in navigator) {
    Object.defineProperty(navigator, 'connection', {
      get: () => ({
        effectiveType: '4g',
        rtt: 50 + Math.random() * 50,
        downlink: 10 + Math.random() * 10,
        saveData: false
      }),
      configurable: true
    });
  }

  // 10. Memory API Spoofing
  if ('deviceMemory' in navigator) {
    Object.defineProperty(navigator, 'deviceMemory', {
      get: () => 8,
      configurable: true
    });
  }

  // 11. Worker Interception (Enhanced)
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;

  window.Worker = function(url, options) {
    if (typeof url === "string" && !url.startsWith("blob:")) {
      const injection = `
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => ${SPOOFED_CONCURRENCY},
          configurable: true
        });
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });
        importScripts(${JSON.stringify(url)});
      `;
      const blob = new OriginalBlob([injection], {
        type: "application/javascript",
      });
      const spoofedUrl = URL.createObjectURL(blob);
      return new OriginalWorker(spoofedUrl, options);
    }
    return new OriginalWorker(url, options);
  };

  window.Blob = function(parts, options = {}) {
    const type = options.type || "";
    const isJavaScript = type.includes("javascript");

    if (isJavaScript) {
      const combined = parts
        .map((p) => (typeof p === "string" ? p : ""))
        .join("");

      const spoofInjection = `
        try {
          Object.defineProperty(self.navigator, 'hardwareConcurrency', {
            get: () => ${SPOOFED_CONCURRENCY},
            configurable: true
          });
          Object.defineProperty(self.navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
          });
        } catch (e) {}
      `;

      const modifiedParts = [spoofInjection + "\n" + combined];
      return new OriginalBlob(modifiedParts, options);
    }

    return new OriginalBlob(parts, options);
  };

  // 12. Canvas Fingerprinting Protection
  const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
  HTMLCanvasElement.prototype.toDataURL = function(...args) {
    // Add slight noise to canvas fingerprinting
    const context = this.getContext('2d');
    if (context) {
      const imageData = context.getImageData(0, 0, this.width, this.height);
      for (let i = 0; i < imageData.data.length; i += 4) {
        if (Math.random() < 0.001) {
          imageData.data[i] = Math.floor(Math.random() * 256);
        }
      }
      context.putImageData(imageData, 0, 0);
    }
    return originalToDataURL.apply(this, args);
  };

  // 13. WebGL Fingerprinting Protection
  const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
  WebGLRenderingContext.prototype.getParameter = function(parameter) {
    if (parameter === this.RENDERER) {
      return 'Intel Iris OpenGL Engine';
    }
    if (parameter === this.VENDOR) {
      return 'Intel Inc.';
    }
    return originalGetParameter.apply(this, arguments);
  };

  // 14. Screen Resolution Consistency
  Object.defineProperty(screen, 'availWidth', {
    get: () => window.innerWidth,
    configurable: true
  });

  Object.defineProperty(screen, 'availHeight', {
    get: () => window.innerHeight,
    configurable: true
  });

  // 15. Date/Time Consistency
  const originalDate = Date;
  const timeOffset = Math.random() * 1000; // Random offset up to 1 second
  
  window.Date = function(...args) {
    if (args.length === 0) {
      return new originalDate(originalDate.now() + timeOffset);
    }
    return new originalDate(...args);
  };
  
  window.Date.now = () => originalDate.now() + timeOffset;
  window.Date.parse = originalDate.parse;
  window.Date.UTC = originalDate.UTC;

  console.log('🛡️ Enhanced stealth script loaded successfully');
})();
