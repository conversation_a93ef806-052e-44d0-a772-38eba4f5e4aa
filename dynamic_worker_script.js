
(() => {
  'use strict';

  console.log('🛡️ Loading dynamic worker interception script...');

  // Dynamic values from browserforge fingerprint
  const DYNAMIC_HARDWARE_CONCURRENCY = 4;
  const DYNAMIC_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  const DYNAMIC_PLATFORM = 'Win32';

  // Store original constructors before any modification
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;
  const OriginalURL = window.URL;

  // Early interception - override before any scripts load
  Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => DYNAMIC_HARDWARE_CONCURRENCY,
    configurable: true
  });

  Object.defineProperty(navigator, 'userAgent', {
    get: () => DYNAMIC_USER_AGENT,
    configurable: true
  });

  Object.defineProperty(navigator, 'platform', {
    get: () => DYNAMIC_PLATFORM,
    configurable: true
  });

  // Comprehensive Worker interception
  window.Worker = function(scriptURL, options) {
    console.log('🔧 [Worker] Intercepting worker creation:', scriptURL);

    if (typeof scriptURL === 'string') {
      // Create injection script for external workers
      const injectionScript = `
        // Worker scope spoofing
        Object.defineProperty(self.navigator, 'hardwareConcurrency', {
          get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
          configurable: true
        });

        Object.defineProperty(self.navigator, 'userAgent', {
          get: () => '${DYNAMIC_USER_AGENT}',
          configurable: true
        });

        Object.defineProperty(self.navigator, 'platform', {
          get: () => '${DYNAMIC_PLATFORM}',
          configurable: true
        });

        Object.defineProperty(self.navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });

        // Import the original script
        try {
          importScripts('${scriptURL}');
        } catch (e) {
          console.error('Failed to import script:', e);
        }
      `;

      // Create blob with injection
      const blob = new OriginalBlob([injectionScript], {
        type: 'application/javascript'
      });

      const blobURL = OriginalURL.createObjectURL(blob);
      console.log('🔧 [Worker] Created spoofed worker with blob URL');

      return new OriginalWorker(blobURL, options);
    }

    return new OriginalWorker(scriptURL, options);
  };

  // Enhanced Blob interception for inline workers
  window.Blob = function(parts, options = {}) {
    const type = options.type || '';

    if (type.includes('javascript') || type.includes('application/javascript')) {
      console.log('🔧 [Blob] Intercepting JavaScript blob creation');

      const originalContent = parts.map(part =>
        typeof part === 'string' ? part : ''
      ).join('');

      // Add spoofing to the beginning of the blob content
      const spoofingInjection = `
        // Blob worker spoofing
        if (typeof self !== 'undefined' && self.navigator) {
          Object.defineProperty(self.navigator, 'hardwareConcurrency', {
            get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
            configurable: true
          });

          Object.defineProperty(self.navigator, 'userAgent', {
            get: () => '${DYNAMIC_USER_AGENT}',
            configurable: true
          });

          Object.defineProperty(self.navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
          });
        }

        // Original content follows
      `;

      const modifiedParts = [spoofingInjection + originalContent];
      return new OriginalBlob(modifiedParts, options);
    }

    return new OriginalBlob(parts, options);
  };

  // Additional protection - override fetch for worker scripts
  const originalFetch = window.fetch;
  window.fetch = function(resource, init) {
    if (typeof resource === 'string' && resource.includes('.js')) {
      console.log('🔧 [Fetch] Intercepting script fetch:', resource);
    }
    return originalFetch.call(this, resource, init);
  };

  console.log('✅ Dynamic worker interception script loaded successfully');
  console.log('📊 Using hardware concurrency:', DYNAMIC_HARDWARE_CONCURRENCY);
})();
