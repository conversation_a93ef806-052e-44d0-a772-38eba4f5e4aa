import asyncio
from enhanced_headless_config import create_stealth_context

async def test_dynamic_worker_bypass():
    """
    Test the dynamic worker bypass specifically for CreepJS
    """
    
    print("🧪 Testing Dynamic Worker Bypass for CreepJS")
    print("=" * 60)
    
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,
        proxy_server=None
    )
    
    try:
        # Get fingerprint values for verification
        fingerprint = config.fingerprint
        expected_hardware_concurrency = fingerprint.navigator.hardwareConcurrency
        expected_user_agent = fingerprint.navigator.userAgent
        expected_platform = fingerprint.navigator.platform
        
        print(f"📊 Expected Hardware Concurrency: {expected_hardware_concurrency}")
        print(f"🌐 Expected User Agent: {expected_user_agent}")
        print(f"💻 Expected Platform: {expected_platform}")
        print()
        
        # Create dynamic script with browserforge values
        dynamic_script = config.create_dynamic_worker_script(fingerprint)
        await context.add_init_script(content=dynamic_script)
        
        page = await context.new_page()
        
        # Inject dynamic values into page context
        await page.add_init_script(f"""
            window.BROWSERFORGE_HARDWARE_CONCURRENCY = {expected_hardware_concurrency};
            window.BROWSERFORGE_USER_AGENT = '{expected_user_agent}';
            window.BROWSERFORGE_PLATFORM = '{expected_platform}';
            console.log('🔧 Dynamic values injected:', window.BROWSERFORGE_HARDWARE_CONCURRENCY);
        """)
        
        # Add CreepJS-specific bypass script
        await context.add_init_script(path="./creepjs_bypass.js")
        
        # Setup page for API access
        await config.setup_page_for_api_access(page)
        
        # Test 1: Verify main thread spoofing
        print("🧪 Test 1: Main Thread Spoofing")
        main_hardware = await page.evaluate("() => navigator.hardwareConcurrency")
        main_user_agent = await page.evaluate("() => navigator.userAgent")
        main_platform = await page.evaluate("() => navigator.platform")
        
        print(f"   Main Thread Hardware Concurrency: {main_hardware}")
        print(f"   Expected: {expected_hardware_concurrency}")
        print(f"   ✅ Match: {main_hardware == expected_hardware_concurrency}")
        print()
        
        # Test 2: Create a worker and test spoofing
        print("🧪 Test 2: Worker Thread Spoofing")
        
        worker_test_result = await page.evaluate(f"""
            new Promise((resolve) => {{
                // Create a simple worker to test spoofing
                const workerCode = `
                    self.postMessage({{
                        hardwareConcurrency: self.navigator.hardwareConcurrency,
                        userAgent: self.navigator.userAgent,
                        platform: self.navigator.platform,
                        webdriver: self.navigator.webdriver
                    }});
                `;
                
                const blob = new Blob([workerCode], {{ type: 'application/javascript' }});
                const worker = new Worker(URL.createObjectURL(blob));
                
                worker.onmessage = function(e) {{
                    resolve(e.data);
                }};
                
                worker.onerror = function(error) {{
                    resolve({{ error: error.message }});
                }};
                
                // Timeout after 5 seconds
                setTimeout(() => {{
                    resolve({{ error: 'Worker timeout' }});
                }}, 5000);
            }})
        """)
        
        if 'error' in worker_test_result:
            print(f"   ❌ Worker test failed: {worker_test_result['error']}")
        else:
            worker_hardware = worker_test_result.get('hardwareConcurrency')
            worker_webdriver = worker_test_result.get('webdriver')
            
            print(f"   Worker Hardware Concurrency: {worker_hardware}")
            print(f"   Expected: {expected_hardware_concurrency}")
            print(f"   ✅ Match: {worker_hardware == expected_hardware_concurrency}")
            print(f"   Worker WebDriver: {worker_webdriver}")
            print(f"   ✅ WebDriver Hidden: {worker_webdriver is None}")
        print()
        
        # Test 3: Test external script worker (simulating CreepJS behavior)
        print("🧪 Test 3: External Script Worker (CreepJS simulation)")
        
        # Create a simple test script
        test_script_content = """
            self.postMessage({
                type: 'external_worker_test',
                hardwareConcurrency: self.navigator.hardwareConcurrency,
                userAgent: self.navigator.userAgent,
                platform: self.navigator.platform,
                webdriver: self.navigator.webdriver
            });
        """
        
        # Save test script
        with open("test_worker_script.js", "w") as f:
            f.write(test_script_content)
        
        external_worker_result = await page.evaluate("""
            new Promise((resolve) => {
                try {
                    const worker = new Worker('./test_worker_script.js');
                    
                    worker.onmessage = function(e) {
                        resolve(e.data);
                    };
                    
                    worker.onerror = function(error) {
                        resolve({ error: error.message });
                    };
                    
                    // Timeout after 5 seconds
                    setTimeout(() => {
                        resolve({ error: 'External worker timeout' });
                    }, 5000);
                } catch (e) {
                    resolve({ error: e.message });
                }
            })
        """)
        
        if 'error' in external_worker_result:
            print(f"   ⚠️ External worker test: {external_worker_result['error']}")
            print("   (This is expected in some environments)")
        else:
            ext_hardware = external_worker_result.get('hardwareConcurrency')
            ext_webdriver = external_worker_result.get('webdriver')
            
            print(f"   External Worker Hardware Concurrency: {ext_hardware}")
            print(f"   Expected: {expected_hardware_concurrency}")
            print(f"   ✅ Match: {ext_hardware == expected_hardware_concurrency}")
            print(f"   External Worker WebDriver: {ext_webdriver}")
            print(f"   ✅ WebDriver Hidden: {ext_webdriver is None}")
        print()
        
        # Test 4: Test CreepJS specifically
        print("🧪 Test 4: CreepJS Real Test")
        
        # Add delays before navigation
        await config.add_random_delays(page, 2000, 4000)
        
        print("   🌐 Navigating to CreepJS...")
        await page.goto("https://abrahamjuliot.github.io/creepjs/")
        await page.wait_for_load_state("domcontentloaded")
        
        # Wait for CreepJS to load and analyze
        print("   ⏳ Waiting for CreepJS analysis...")
        await config.add_random_delays(page, 15000, 20000)
        
        # Simulate human behavior
        await config.simulate_human_behavior(page)
        
        # Check for API access issues
        content = await page.content()
        
        if "400" in content:
            print("   ❌ API access denied detected")
        elif "access denied" in content.lower():
            print("   ❌ Access denied detected")
        elif "fingerprint" in content.lower():
            print("   ✅ CreepJS loaded successfully")
            
            # Try to extract hardware concurrency from page
            try:
                creepjs_hardware = await page.evaluate("""
                    () => {
                        // Try to find hardware concurrency in the page
                        const elements = document.querySelectorAll('*');
                        for (let el of elements) {
                            if (el.textContent && el.textContent.includes('hardware')) {
                                return el.textContent;
                            }
                        }
                        return 'Not found';
                    }
                """)
                print(f"   📊 CreepJS Hardware Detection: {creepjs_hardware}")
            except Exception as e:
                print(f"   ℹ️ Could not extract CreepJS data: {e}")
        else:
            print("   ⚠️ Unclear CreepJS result")
        
        # Take screenshot
        await page.screenshot(path="dynamic_worker_test.png", full_page=True)
        print("   📸 Screenshot saved: dynamic_worker_test.png")
        
        print("\n" + "=" * 60)
        print("📋 SUMMARY:")
        print(f"✅ Using dynamic hardware concurrency: {expected_hardware_concurrency}")
        print(f"✅ Using dynamic user agent: {expected_user_agent[:50]}...")
        print(f"✅ Using dynamic platform: {expected_platform}")
        print("✅ Worker interception implemented")
        print("✅ CreepJS bypass script loaded")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        
    finally:
        await browser.close()
        await playwright_instance.stop()

if __name__ == "__main__":
    print("🎭 Dynamic Worker Bypass Test Suite")
    print("Testing browserforge integration with worker interception")
    print()
    
    asyncio.run(test_dynamic_worker_bypass())
    
    print("\n🔧 Next Steps:")
    print("1. Check the screenshot for visual verification")
    print("2. If CreepJS shows correct hardware concurrency, the fix is working")
    print("3. If you still see API access denied, try using a different IP/proxy")
    print("4. The dynamic values should now match your browserforge fingerprint")
