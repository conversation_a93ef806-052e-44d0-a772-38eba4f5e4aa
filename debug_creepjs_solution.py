import asyncio
import j<PERSON>
from enhanced_headless_config import create_stealth_context, create_dynamic_worker_script

async def debug_creepjs_solution():
    """
    Comprehensive debugging and testing for CreepJS high trust score
    """
    
    print("🔧 CreepJS Solution Debugging & Testing")
    print("=" * 60)
    
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,
        proxy_server=None
    )
    
    try:
        # Get fingerprint values
        fingerprint = config.fingerprint
        expected_hardware = fingerprint.navigator.hardwareConcurrency
        expected_user_agent = fingerprint.navigator.userAgent
        expected_platform = fingerprint.navigator.platform
        
        print(f"📊 BrowserForge Fingerprint Values:")
        print(f"   Hardware Concurrency: {expected_hardware}")
        print(f"   User Agent: {expected_user_agent}")
        print(f"   Platform: {expected_platform}")
        print()
        
        # Step 1: Create and inject dynamic worker script
        print("🔧 Step 1: Creating dynamic worker script...")
        try:
            dynamic_script = create_dynamic_worker_script(fingerprint)
            # Save to file and inject
            with open("dynamic_worker_script.js", "w", encoding="utf-8") as f:
                f.write(dynamic_script)
            await context.add_init_script(path="./dynamic_worker_script.js")
            print("   ✅ Dynamic worker script created and injected")
        except Exception as e:
            print(f"   ❌ Dynamic worker script failed: {e}")
            return
        
        # Step 2: Create page and inject values
        print("🔧 Step 2: Creating page and injecting values...")
        page = await context.new_page()
        
        # Inject dynamic values into page context
        dynamic_values_script = f"""
            window.BROWSERFORGE_HARDWARE_CONCURRENCY = {expected_hardware};
            window.BROWSERFORGE_USER_AGENT = '{expected_user_agent}';
            window.BROWSERFORGE_PLATFORM = '{expected_platform}';
            console.log('🔧 Dynamic values injected successfully');
        """
        with open("dynamic_values.js", "w", encoding="utf-8") as f:
            f.write(dynamic_values_script)
        await page.add_init_script(path="./dynamic_values.js")
        
        # Add high trust bypass script
        await context.add_init_script(path="./high_trust_creepjs_bypass.js")
        print("   ✅ High trust bypass script loaded")
        
        # Step 3: Setup page for API access
        print("🔧 Step 3: Setting up page for API access...")
        await config.setup_page_for_api_access(page)
        print("   ✅ Page configured for API access")
        
        # Step 4: Test main thread spoofing
        print("🔧 Step 4: Testing main thread spoofing...")
        main_values = await page.evaluate("""
            () => ({
                hardwareConcurrency: navigator.hardwareConcurrency,
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                webdriver: navigator.webdriver,
                plugins: navigator.plugins.length,
                mimeTypes: navigator.mimeTypes.length,
                languages: navigator.languages,
                chrome: !!window.chrome
            })
        """)
        
        print(f"   Hardware Concurrency: {main_values['hardwareConcurrency']} (expected: {expected_hardware})")
        print(f"   ✅ Match: {main_values['hardwareConcurrency'] == expected_hardware}")
        print(f"   User Agent: {main_values['userAgent'][:50]}...")
        print(f"   Platform: {main_values['platform']}")
        print(f"   WebDriver: {main_values['webdriver']}")
        print(f"   Plugins: {main_values['plugins']}")
        print(f"   MIME Types: {main_values['mimeTypes']}")
        print(f"   Languages: {main_values['languages']}")
        print(f"   Chrome Object: {main_values['chrome']}")
        print()
        
        # Step 5: Test worker spoofing
        print("🔧 Step 5: Testing worker spoofing...")
        worker_test = await page.evaluate(f"""
            new Promise((resolve) => {{
                const workerCode = `
                    self.postMessage({{
                        hardwareConcurrency: self.navigator.hardwareConcurrency,
                        userAgent: self.navigator.userAgent,
                        platform: self.navigator.platform,
                        webdriver: self.navigator.webdriver,
                        languages: self.navigator.languages
                    }});
                `;
                
                const blob = new Blob([workerCode], {{ type: 'application/javascript' }});
                const worker = new Worker(URL.createObjectURL(blob));
                
                worker.onmessage = (e) => resolve(e.data);
                worker.onerror = (e) => resolve({{ error: e.message }});
                
                setTimeout(() => resolve({{ error: 'timeout' }}), 5000);
            }})
        """)
        
        if 'error' in worker_test:
            print(f"   ❌ Worker test failed: {worker_test['error']}")
        else:
            print(f"   Worker Hardware Concurrency: {worker_test['hardwareConcurrency']} (expected: {expected_hardware})")
            print(f"   ✅ Worker Match: {worker_test['hardwareConcurrency'] == expected_hardware}")
            print(f"   Worker WebDriver: {worker_test['webdriver']}")
            print(f"   Worker Languages: {worker_test['languages']}")
        print()
        
        # Step 6: Test CreepJS
        print("🔧 Step 6: Testing CreepJS...")
        
        # Add delays before navigation
        await config.add_random_delays(page, 2000, 4000)
        
        print("   🌐 Navigating to CreepJS...")
        await page.goto("https://abrahamjuliot.github.io/creepjs/")
        await page.wait_for_load_state("domcontentloaded")
        
        # Wait for CreepJS to analyze
        print("   ⏳ Waiting for CreepJS analysis (20 seconds)...")
        await config.add_random_delays(page, 20000, 25000)
        
        # Simulate human behavior
        await config.simulate_human_behavior(page)
        
        # Check for errors
        content = await page.content()
        
        if "400" in content:
            print("   ❌ API access denied detected")
        elif "access denied" in content.lower():
            print("   ❌ Access denied detected")
        else:
            print("   ✅ No API access errors detected")
        
        # Try to extract trust score and fingerprint data
        try:
            creepjs_data = await page.evaluate("""
                () => {
                    const results = {};
                    
                    // Look for trust score
                    const trustElements = document.querySelectorAll('[data-trust-score], .trust-score, .score');
                    for (let el of trustElements) {
                        if (el.textContent && el.textContent.includes('%')) {
                            results.trustScore = el.textContent.trim();
                            break;
                        }
                    }
                    
                    // Look for hardware concurrency detection
                    const hardwareElements = document.querySelectorAll('*');
                    for (let el of hardwareElements) {
                        if (el.textContent && el.textContent.toLowerCase().includes('hardware')) {
                            results.hardwareDetection = el.textContent.trim();
                            break;
                        }
                    }
                    
                    // Look for fingerprint score
                    const scoreElements = document.querySelectorAll('[data-fingerprint-score], .fingerprint-score');
                    for (let el of scoreElements) {
                        if (el.textContent) {
                            results.fingerprintScore = el.textContent.trim();
                            break;
                        }
                    }
                    
                    // Check for any detection warnings
                    const warningElements = document.querySelectorAll('.warning, .error, .detected');
                    results.warnings = [];
                    for (let el of warningElements) {
                        if (el.textContent) {
                            results.warnings.push(el.textContent.trim());
                        }
                    }
                    
                    return results;
                }
            """)
            
            print("   📊 CreepJS Analysis Results:")
            if creepjs_data.get('trustScore'):
                print(f"      Trust Score: {creepjs_data['trustScore']}")
            if creepjs_data.get('fingerprintScore'):
                print(f"      Fingerprint Score: {creepjs_data['fingerprintScore']}")
            if creepjs_data.get('hardwareDetection'):
                print(f"      Hardware Detection: {creepjs_data['hardwareDetection']}")
            if creepjs_data.get('warnings'):
                print(f"      Warnings: {len(creepjs_data['warnings'])} detected")
                for warning in creepjs_data['warnings'][:3]:  # Show first 3 warnings
                    print(f"         - {warning[:100]}...")
            
        except Exception as e:
            print(f"   ⚠️ Could not extract CreepJS data: {e}")
        
        # Take screenshot
        await page.screenshot(path="creepjs_debug_test.png", full_page=True)
        print("   📸 Screenshot saved: creepjs_debug_test.png")
        
        # Step 7: Final verification
        print("🔧 Step 7: Final verification...")
        
        final_check = await page.evaluate(f"""
            () => {{
                const issues = [];
                
                // Check hardware concurrency consistency
                if (navigator.hardwareConcurrency !== {expected_hardware}) {{
                    issues.push(`Hardware concurrency mismatch: ${{navigator.hardwareConcurrency}} vs {expected_hardware}`);
                }}
                
                // Check webdriver
                if (navigator.webdriver !== undefined) {{
                    issues.push(`WebDriver detected: ${{navigator.webdriver}}`);
                }}
                
                // Check chrome object
                if (!window.chrome) {{
                    issues.push('Chrome object missing');
                }}
                
                // Check plugins
                if (navigator.plugins.length === 0) {{
                    issues.push('No plugins detected');
                }}
                
                return {{
                    issues: issues,
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    webdriver: navigator.webdriver,
                    chromePresent: !!window.chrome,
                    pluginCount: navigator.plugins.length
                }};
            }}
        """)
        
        if final_check['issues']:
            print("   ❌ Issues detected:")
            for issue in final_check['issues']:
                print(f"      - {issue}")
        else:
            print("   ✅ All checks passed!")
        
        print(f"   Final Hardware Concurrency: {final_check['hardwareConcurrency']}")
        print(f"   WebDriver Status: {final_check['webdriver']}")
        print(f"   Chrome Object Present: {final_check['chromePresent']}")
        print(f"   Plugin Count: {final_check['pluginCount']}")
        
        print("\n" + "=" * 60)
        print("📋 SUMMARY:")
        print(f"✅ Dynamic values from BrowserForge: {expected_hardware} cores")
        print(f"✅ Worker interception: {'Working' if 'error' not in worker_test else 'Failed'}")
        print(f"✅ CreepJS access: {'Success' if '400' not in content else 'Failed'}")
        print(f"✅ Final verification: {'Passed' if not final_check['issues'] else 'Issues detected'}")
        
    except Exception as e:
        print(f"❌ Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await browser.close()
        await playwright_instance.stop()

if __name__ == "__main__":
    print("🎭 CreepJS Solution Debugging Suite")
    print("Testing all components for high trust score")
    print()
    
    asyncio.run(debug_creepjs_solution())
    
    print("\n🔧 Next Steps:")
    print("1. Check the screenshot for visual verification")
    print("2. Look for trust score percentage in the output")
    print("3. Verify hardware concurrency matches BrowserForge values")
    print("4. If issues persist, check browser console logs")
    print("5. Consider using a residential proxy if API access is still denied")
