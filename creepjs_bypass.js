(() => {
  "use strict";

  // Advanced CreepJS bypass script with dynamic values
  // Designed specifically to handle CreepJS detection methods and worker interception

  console.log("🛡️ Loading advanced CreepJS bypass script...");

  // Get dynamic values from window if available (set by Python)
  const DYNAMIC_HARDWARE_CONCURRENCY =
    window.BROWSERFORGE_HARDWARE_CONCURRENCY || 8;
  const DYNAMIC_USER_AGENT =
    window.BROWSERFORGE_USER_AGENT ||
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
  const DYNAMIC_PLATFORM = window.BROWSERFORGE_PLATFORM || "Win32";

  console.log(
    "📊 Using dynamic hardware concurrency:",
    DYNAMIC_HARDWARE_CONCURRENCY
  );

  // Store original constructors BEFORE any modifications
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;
  const OriginalURL = window.URL;
  const OriginalFetch = window.fetch;

  // 1. WebDriver Detection Bypass (Multiple layers)
  Object.defineProperty(navigator, "webdriver", {
    get: () => undefined,
    configurable: true,
  });

  // Remove webdriver from window and document
  delete window.webdriver;
  delete window.document.webdriver;
  delete window.navigator.webdriver;

  // Override getAttribute for webdriver detection
  const originalGetAttribute = Element.prototype.getAttribute;
  Element.prototype.getAttribute = function (name) {
    if (name === "webdriver") {
      return null;
    }
    return originalGetAttribute.call(this, name);
  };

  // 2. Automation Detection Bypass
  Object.defineProperty(navigator, "automation", {
    get: () => undefined,
    configurable: true,
  });

  // 3. Hardware Concurrency (Dynamic from browserforge)
  Object.defineProperty(navigator, "hardwareConcurrency", {
    get: () => DYNAMIC_HARDWARE_CONCURRENCY,
    configurable: true,
  });

  // 4. Enhanced Chrome Object
  if (!window.chrome || Object.keys(window.chrome).length === 0) {
    Object.defineProperty(window, "chrome", {
      get: () => ({
        runtime: {
          onConnect: undefined,
          onMessage: undefined,
          PlatformOs: {
            MAC: "mac",
            WIN: "win",
            ANDROID: "android",
            CROS: "cros",
            LINUX: "linux",
            OPENBSD: "openbsd",
          },
          PlatformArch: {
            ARM: "arm",
            X86_32: "x86-32",
            X86_64: "x86-64",
          },
        },
        csi: () => ({}),
        loadTimes: () => ({
          requestTime: performance.now() / 1000,
          startLoadTime: performance.now() / 1000,
          commitLoadTime: performance.now() / 1000,
          finishDocumentLoadTime: performance.now() / 1000,
          finishLoadTime: performance.now() / 1000,
          firstPaintTime: performance.now() / 1000,
          firstPaintAfterLoadTime: 0,
          navigationType: "Other",
        }),
        app: {
          isInstalled: false,
        },
      }),
      configurable: true,
    });
  }

  // 5. Enhanced Plugin and MIME Type Configuration
  const mockPlugins = [
    {
      name: "Chrome PDF Plugin",
      description: "Portable Document Format",
      filename: "internal-pdf-viewer",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
      },
    },
    {
      name: "Chrome PDF Viewer",
      description: "Portable Document Format",
      filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
      length: 1,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
      },
    },
    {
      name: "Native Client",
      description: "Native Client",
      filename: "internal-nacl-plugin",
      length: 2,
      0: {
        type: "application/x-nacl",
        suffixes: "",
        description: "Native Client Executable",
      },
      1: {
        type: "application/x-pnacl",
        suffixes: "",
        description: "Portable Native Client Executable",
      },
    },
  ];

  Object.defineProperty(navigator, "plugins", {
    get: () => mockPlugins,
    configurable: true,
  });

  Object.defineProperty(navigator, "mimeTypes", {
    get: () => ({
      length: 4,
      0: {
        type: "application/pdf",
        suffixes: "pdf",
        description: "Portable Document Format",
      },
      1: {
        type: "application/x-nacl",
        suffixes: "",
        description: "Native Client Executable",
      },
      2: {
        type: "application/x-pnacl",
        suffixes: "",
        description: "Portable Native Client Executable",
      },
      3: { type: "text/plain", suffixes: "txt", description: "Plain Text" },
    }),
    configurable: true,
  });

  // 6. Language and Locale Consistency
  Object.defineProperty(navigator, "language", {
    get: () => "en-US",
    configurable: true,
  });

  Object.defineProperty(navigator, "languages", {
    get: () => ["en-US", "en"],
    configurable: true,
  });

  // 7. Platform Information
  Object.defineProperty(navigator, "platform", {
    get: () => "Win32",
    configurable: true,
  });

  Object.defineProperty(navigator, "userAgent", {
    get: () =>
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    configurable: true,
  });

  // 8. Screen and Window Consistency
  Object.defineProperty(screen, "availWidth", {
    get: () => window.innerWidth,
    configurable: true,
  });

  Object.defineProperty(screen, "availHeight", {
    get: () => window.innerHeight,
    configurable: true,
  });

  // 9. Permissions API
  const originalPermissionsQuery = navigator.permissions.query;
  navigator.permissions.query = (parameters) => {
    const permission = parameters.name;
    if (permission === "notifications") {
      return Promise.resolve({ state: "granted" });
    }
    return originalPermissionsQuery.call(navigator.permissions, parameters);
  };

  // 10. Battery API Spoofing
  if ("getBattery" in navigator) {
    navigator.getBattery = () => {
      return Promise.resolve({
        charging: true,
        chargingTime: 0,
        dischargingTime: Infinity,
        level: 0.85,
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => {},
      });
    };
  }

  // 11. Connection API
  if ("connection" in navigator) {
    Object.defineProperty(navigator, "connection", {
      get: () => ({
        effectiveType: "4g",
        rtt: 50,
        downlink: 10,
        saveData: false,
      }),
      configurable: true,
    });
  }

  // 12. Device Memory
  if ("deviceMemory" in navigator) {
    Object.defineProperty(navigator, "deviceMemory", {
      get: () => 8,
      configurable: true,
    });
  }

  // 13. Canvas Fingerprinting Protection
  const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
  HTMLCanvasElement.prototype.toDataURL = function (...args) {
    // Add minimal noise to avoid detection
    const context = this.getContext("2d");
    if (context) {
      const imageData = context.getImageData(0, 0, this.width, this.height);
      // Add very subtle noise
      for (let i = 0; i < imageData.data.length; i += 4) {
        if (Math.random() < 0.001) {
          imageData.data[i] = Math.min(
            255,
            imageData.data[i] + Math.floor(Math.random() * 3) - 1
          );
        }
      }
      context.putImageData(imageData, 0, 0);
    }
    return originalToDataURL.apply(this, args);
  };

  // 14. WebGL Fingerprinting Protection
  const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
  WebGLRenderingContext.prototype.getParameter = function (parameter) {
    if (parameter === this.RENDERER) {
      return "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)";
    }
    if (parameter === this.VENDOR) {
      return "Google Inc. (Intel)";
    }
    if (parameter === this.VERSION) {
      return "WebGL 1.0 (OpenGL ES 2.0 Chromium)";
    }
    if (parameter === this.SHADING_LANGUAGE_VERSION) {
      return "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)";
    }
    return originalGetParameter.apply(this, arguments);
  };

  // 15. Enhanced Worker Interception for CreepJS
  console.log("🔧 Setting up comprehensive worker interception...");

  window.Worker = function (scriptURL, options) {
    console.log("🔧 [Worker] Intercepting worker creation:", scriptURL);

    if (typeof scriptURL === "string") {
      // Create comprehensive injection script
      const injectionScript = `
        console.log('🛡️ Worker scope spoofing active');

        // Spoof navigator properties in worker scope
        Object.defineProperty(self.navigator, 'hardwareConcurrency', {
          get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
          configurable: true
        });

        Object.defineProperty(self.navigator, 'userAgent', {
          get: () => '${DYNAMIC_USER_AGENT}',
          configurable: true
        });

        Object.defineProperty(self.navigator, 'platform', {
          get: () => '${DYNAMIC_PLATFORM}',
          configurable: true
        });

        Object.defineProperty(self.navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });

        // Remove automation indicators in worker
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete self.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

        // Import the original script
        try {
          importScripts('${scriptURL}');
        } catch (e) {
          console.error('Worker script import failed:', e);
        }
      `;

      // Create blob with injection
      const blob = new OriginalBlob([injectionScript], {
        type: "application/javascript",
      });

      const blobURL = OriginalURL.createObjectURL(blob);
      console.log("🔧 [Worker] Created spoofed worker with dynamic values");

      return new OriginalWorker(blobURL, options);
    }

    return new OriginalWorker(scriptURL, options);
  };

  // Enhanced Blob interception
  window.Blob = function (parts, options = {}) {
    const type = options.type || "";

    if (
      type.includes("javascript") ||
      type.includes("application/javascript")
    ) {
      console.log("🔧 [Blob] Intercepting JavaScript blob for worker");

      const originalContent = parts
        .map((part) => (typeof part === "string" ? part : ""))
        .join("");

      // Add comprehensive spoofing to blob content
      const spoofingInjection = `
        // Blob worker comprehensive spoofing
        if (typeof self !== 'undefined') {
          Object.defineProperty(self.navigator, 'hardwareConcurrency', {
            get: () => ${DYNAMIC_HARDWARE_CONCURRENCY},
            configurable: true
          });

          Object.defineProperty(self.navigator, 'userAgent', {
            get: () => '${DYNAMIC_USER_AGENT}',
            configurable: true
          });

          Object.defineProperty(self.navigator, 'platform', {
            get: () => '${DYNAMIC_PLATFORM}',
            configurable: true
          });

          Object.defineProperty(self.navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
          });

          console.log('🛡️ Blob worker spoofing applied, hardwareConcurrency:', ${DYNAMIC_HARDWARE_CONCURRENCY});
        }

        // Original blob content follows:
      `;

      const modifiedParts = [spoofingInjection + originalContent];
      return new OriginalBlob(modifiedParts, options);
    }

    return new OriginalBlob(parts, options);
  };

  // 16. Date/Time Consistency
  const timeOffset = 0; // No offset for consistency
  const originalDate = Date;
  window.Date = function (...args) {
    if (args.length === 0) {
      return new originalDate(originalDate.now() + timeOffset);
    }
    return new originalDate(...args);
  };
  window.Date.now = () => originalDate.now() + timeOffset;
  window.Date.parse = originalDate.parse;
  window.Date.UTC = originalDate.UTC;

  // 17. Remove automation indicators
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
  delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

  console.log("✅ CreepJS bypass script loaded successfully");
})();
