import asyncio
import random
import json
from datetime import datetime, timed<PERSON>ta
from playwright.async_api import async_playwright
from browserforge.headers import <PERSON>er<PERSON>enerator
from browserforge.fingerprints import FingerprintGenerator, Screen
from browserforge.injectors.playwright import AsyncNewContext

class EnhancedHeadlessConfig:
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
        ]
        
    def get_enhanced_chrome_args(self):
        """Enhanced Chrome arguments for better stealth and API access"""
        return [
            "--no-sandbox",
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--disable-extensions-file-access-check",
            "--disable-extensions-http-throttling",
            "--disable-extensions-except",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-background-networking",
            "--enable-features=NetworkService,NetworkServiceInProcess",
            "--disable-breakpad",
            "--disable-client-side-phishing-detection",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--disable-dev-shm-usage",
            "--disable-extensions",
            "--disable-features=TranslateUI",
            "--disable-hang-monitor",
            "--disable-ipc-flooding-protection",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-web-security",
            "--metrics-recording-only",
            "--no-first-run",
            "--no-default-browser-check",
            "--password-store=basic",
            "--use-mock-keychain",
            "--ignore-certificate-errors",
            "--ignore-ssl-errors",
            "--ignore-certificate-errors-spki-list",
            "--ignore-ssl-errors-ignore-cert-errors",
            "--disable-gpu",
            "--disable-software-rasterizer",
            # Additional args for API access and stealth
            "--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer",
            "--disable-ipc-flooding-protection",
            "--enable-automation=false",
            "--exclude-switches=enable-automation",
            "--disable-blink-features=AutomationControlled",
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

    def get_comprehensive_plugins(self):
        """Comprehensive plugin and MIME type configuration"""
        return {
            "plugins": [
                {
                    "name": "Chrome PDF Viewer",
                    "description": "Portable Document Format",
                    "filename": "internal-pdf-viewer",
                    "mimeTypes": [
                        {
                            "type": "application/pdf",
                            "suffixes": "pdf",
                            "description": "Portable Document Format",
                            "enabledPlugin": "Chrome PDF Viewer"
                        }
                    ]
                },
                {
                    "name": "Chromium PDF Viewer",
                    "description": "Portable Document Format",
                    "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    "mimeTypes": [
                        {
                            "type": "application/pdf",
                            "suffixes": "pdf",
                            "description": "Portable Document Format",
                            "enabledPlugin": "Chromium PDF Viewer"
                        }
                    ]
                },
                {
                    "name": "Microsoft Edge PDF Viewer",
                    "description": "Portable Document Format",
                    "filename": "pdf",
                    "mimeTypes": [
                        {
                            "type": "application/pdf",
                            "suffixes": "pdf",
                            "description": "Portable Document Format",
                            "enabledPlugin": "Microsoft Edge PDF Viewer"
                        }
                    ]
                },
                {
                    "name": "WebKit built-in PDF",
                    "description": "Portable Document Format",
                    "filename": "internal-pdf-viewer",
                    "mimeTypes": [
                        {
                            "type": "application/pdf",
                            "suffixes": "pdf",
                            "description": "Portable Document Format",
                            "enabledPlugin": "WebKit built-in PDF"
                        }
                    ]
                }
            ],
            "mimeTypes": [
                "Portable Document Format~~application/pdf~~pdf",
                "Native Client Executable~~application/x-nacl~~",
                "Portable Native Client Executable~~application/x-pnacl~~"
            ]
        }

    async def create_enhanced_browser(self, headless=True, proxy_server=None):
        """Create browser with enhanced anti-detection"""
        p = await async_playwright().start()
        
        launch_options = {
            "headless": headless,
            "args": self.get_enhanced_chrome_args()
        }
        
        if proxy_server:
            launch_options["proxy"] = {"server": proxy_server}
            
        browser = await p.chromium.launch(**launch_options)
        return browser, p

    def create_enhanced_fingerprint(self):
        """Create enhanced fingerprint with comprehensive settings"""
        screen = Screen(
            min_width=1280,
            max_width=1920,
            min_height=720,
            max_height=1080
        )
        
        fingerprints = FingerprintGenerator(screen=screen)
        fingerprint = fingerprints.generate(browser='chrome', os='windows')
        
        # Ensure realistic screen dimensions
        if fingerprint.screen.innerWidth < 1280:
            fingerprint.screen.innerWidth = 1280
        if fingerprint.screen.innerHeight < 720:
            fingerprint.screen.innerHeight = 720
        if fingerprint.screen.clientWidth < 1280:
            fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
        if fingerprint.screen.clientHeight < 720:
            fingerprint.screen.clientHeight = fingerprint.screen.innerHeight - 100
            
        # Enhanced plugin configuration
        fingerprint.pluginsData = self.get_comprehensive_plugins()
        
        # Enhanced navigator properties
        fingerprint.navigator.userAgentData['platformVersion'] = '15.0.0'
        fingerprint.navigator.userAgent = random.choice(self.user_agents)
        
        return fingerprint

    async def add_random_delays(self, page, min_delay=1000, max_delay=3000):
        """Add random delays to simulate human behavior"""
        delay = random.randint(min_delay, max_delay)
        await page.wait_for_timeout(delay)

    async def simulate_human_behavior(self, page):
        """Simulate human-like behavior"""
        # Random mouse movements
        await page.mouse.move(
            random.randint(100, 800), 
            random.randint(100, 600)
        )
        await self.add_random_delays(page, 500, 1500)
        
        # Random scroll
        await page.evaluate("window.scrollBy(0, Math.random() * 200)")
        await self.add_random_delays(page, 300, 800)

    async def setup_permissions(self, context):
        """Setup permissions to avoid prompts"""
        await context.grant_permissions([
            'geolocation',
            'notifications',
            'camera',
            'microphone'
        ])

    def get_realistic_headers(self):
        """Get realistic headers for API access and CreepJS"""
        return {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Ch-Ua-Platform-Version': '"15.0.0"',
            'Sec-Ch-Ua-Arch': '"x86"',
            'Sec-Ch-Ua-Bitness': '"64"',
            'Sec-Ch-Ua-Model': '""',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Dnt': '1',
            'Sec-Gpc': '1'
        }

    async def setup_page_for_api_access(self, page):
        """Setup page with realistic headers and behavior for API access"""
        # Set realistic headers
        await page.set_extra_http_headers(self.get_realistic_headers())

        # Set realistic viewport
        await page.set_viewport_size({"width": 1366, "height": 768})

        # Add request interception to modify headers
        await page.route("**/*", self._handle_request)

    async def _handle_request(self, route):
        """Handle requests to add realistic headers"""
        headers = route.request.headers

        # Add missing headers that might be expected
        if 'referer' not in headers:
            headers['referer'] = 'https://www.google.com/'

        if 'origin' not in headers and route.request.method == 'POST':
            headers['origin'] = route.request.url

        await route.continue_(headers=headers)

async def create_stealth_context(headless=True, proxy_server=None):
    """Main function to create a stealth browser context"""
    config = EnhancedHeadlessConfig()

    # Create browser
    browser, playwright = await config.create_enhanced_browser(headless, proxy_server)

    # Create fingerprint
    fingerprint = config.create_enhanced_fingerprint()

    # Create context with fingerprint
    context = await AsyncNewContext(browser, fingerprint=fingerprint)

    # Setup permissions
    await config.setup_permissions(context)

    # Store fingerprint data for dynamic injection
    config.fingerprint = fingerprint

    return browser, context, playwright, config

def create_dynamic_worker_script(fingerprint):
    """Create dynamic worker interception script with browserforge values"""
    hardware_concurrency = fingerprint.navigator.hardwareConcurrency
    user_agent = fingerprint.navigator.userAgent
    platform = fingerprint.navigator.platform

    script_content = f"""
(() => {{
  'use strict';

  console.log('🛡️ Loading dynamic worker interception script...');

  // Dynamic values from browserforge fingerprint
  const DYNAMIC_HARDWARE_CONCURRENCY = {hardware_concurrency};
  const DYNAMIC_USER_AGENT = '{user_agent}';
  const DYNAMIC_PLATFORM = '{platform}';

  // Store original constructors before any modification
  const OriginalWorker = window.Worker;
  const OriginalBlob = window.Blob;
  const OriginalURL = window.URL;

  // Early interception - override before any scripts load
  Object.defineProperty(navigator, 'hardwareConcurrency', {{
    get: () => DYNAMIC_HARDWARE_CONCURRENCY,
    configurable: true
  }});

  Object.defineProperty(navigator, 'userAgent', {{
    get: () => DYNAMIC_USER_AGENT,
    configurable: true
  }});

  Object.defineProperty(navigator, 'platform', {{
    get: () => DYNAMIC_PLATFORM,
    configurable: true
  }});

  // Comprehensive Worker interception
  window.Worker = function(scriptURL, options) {{
    console.log('🔧 [Worker] Intercepting worker creation:', scriptURL);

    if (typeof scriptURL === 'string') {{
      // Create injection script for external workers
      const injectionScript = `
        // Worker scope spoofing
        Object.defineProperty(self.navigator, 'hardwareConcurrency', {{
          get: () => ${{DYNAMIC_HARDWARE_CONCURRENCY}},
          configurable: true
        }});

        Object.defineProperty(self.navigator, 'userAgent', {{
          get: () => '${{DYNAMIC_USER_AGENT}}',
          configurable: true
        }});

        Object.defineProperty(self.navigator, 'platform', {{
          get: () => '${{DYNAMIC_PLATFORM}}',
          configurable: true
        }});

        Object.defineProperty(self.navigator, 'webdriver', {{
          get: () => undefined,
          configurable: true
        }});

        // Import the original script
        try {{
          importScripts('${{scriptURL}}');
        }} catch (e) {{
          console.error('Failed to import script:', e);
        }}
      `;

      // Create blob with injection
      const blob = new OriginalBlob([injectionScript], {{
        type: 'application/javascript'
      }});

      const blobURL = OriginalURL.createObjectURL(blob);
      console.log('🔧 [Worker] Created spoofed worker with blob URL');

      return new OriginalWorker(blobURL, options);
    }}

    return new OriginalWorker(scriptURL, options);
  }};

  // Enhanced Blob interception for inline workers
  window.Blob = function(parts, options = {{}}) {{
    const type = options.type || '';

    if (type.includes('javascript') || type.includes('application/javascript')) {{
      console.log('🔧 [Blob] Intercepting JavaScript blob creation');

      const originalContent = parts.map(part =>
        typeof part === 'string' ? part : ''
      ).join('');

      // Add spoofing to the beginning of the blob content
      const spoofingInjection = `
        // Blob worker spoofing
        if (typeof self !== 'undefined' && self.navigator) {{
          Object.defineProperty(self.navigator, 'hardwareConcurrency', {{
            get: () => ${{DYNAMIC_HARDWARE_CONCURRENCY}},
            configurable: true
          }});

          Object.defineProperty(self.navigator, 'userAgent', {{
            get: () => '${{DYNAMIC_USER_AGENT}}',
            configurable: true
          }});

          Object.defineProperty(self.navigator, 'webdriver', {{
            get: () => undefined,
            configurable: true
          }});
        }}

        // Original content follows
      `;

      const modifiedParts = [spoofingInjection + originalContent];
      return new OriginalBlob(modifiedParts, options);
    }}

    return new OriginalBlob(parts, options);
  }};

  // Additional protection - override fetch for worker scripts
  const originalFetch = window.fetch;
  window.fetch = function(resource, init) {{
    if (typeof resource === 'string' && resource.includes('.js')) {{
      console.log('🔧 [Fetch] Intercepting script fetch:', resource);
    }}
    return originalFetch.call(this, resource, init);
  }};

  console.log('✅ Dynamic worker interception script loaded successfully');
  console.log('📊 Using hardware concurrency:', DYNAMIC_HARDWARE_CONCURRENCY);
}})();
"""

    return script_content

# Example usage
async def test_stealth_mode():
    browser, context, playwright, config = await create_stealth_context(headless=True)
    
    try:
        page = await context.new_page()
        
        # Test on headless detection site
        await page.goto("https://infosimples.github.io/detect-headless/")
        await config.simulate_human_behavior(page)
        
        # Take screenshot
        await page.screenshot(path="stealth_test.png")
        
        # Get page content for analysis
        content = await page.content()
        print("Headless detection test completed")
        
    finally:
        await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(test_stealth_mode())
