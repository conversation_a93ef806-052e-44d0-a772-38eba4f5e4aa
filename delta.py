import asyncio
import random
import json
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
from browserforge.headers import Header<PERSON>enerator
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import AsyncNewContext



async def main():
    
    date= datetime.today()
    current_date_radioUi= date.strftime("%d-%m-%Y")
    current_date_selectorUi= date.strftime("%m-%d-%Y")

    proxy_server = "http://127.0.0.1:8080"
    async with  async_playwright() as p:
     browser = await p.chromium.launch( headless=True,slow_mo=1000)
    #  proxy={"server": proxy_server},
    #  args=["--ignore-certificate-errors"])
     
     headers = HeaderGenerator(  )
     headers.generate()
    #  print(headers)
     screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    #  ScreenFingerprint= ScreenFingerprint.avail
   
    
     fingerprints = FingerprintGenerator(screen=screen , )
     fingerprint= fingerprints.generate(browser='chrome', os='windows')
     if fingerprint.screen.innerWidth < 1000:
      fingerprint.screen.innerWidth = 1280
     if fingerprint.screen.innerHeight < 600:
      fingerprint.screen.innerHeight = 720
     if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     if fingerprint.screen.clientHeight < 1000:
           fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
     fingerprint.pluginsData = {
    "plugins": [
        {
            "name": "Chrome PDF Viewer",
            "description": "Portable Document Format",
            "filename": "internal-pdf-viewer",
            "mimeTypes": [
                {
                    "type": "application/pdf",
                    "suffixes": "pdf",
                    "description": "Portable Document Format",
                    "enabledPlugin": "Chrome PDF Viewer"
                }
            ]
        }
    ],
    "mimeTypes": ["Portable Document Format~~application/pdf~~pdf"]
}
     fingerprint.navigator.userAgentData['platformVersion'] = '10.0.0'
    #  print(fingerprint)
     
     context = await AsyncNewContext(browser, fingerprint= fingerprint)
     

     
     worker_thread= fingerprint.navigator.hardwareConcurrency
     print(f"fingerprint thread {worker_thread}")
     # Step 1: Read the original JS template
     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()

# Step 2: Replace placeholder with dynamic value
      
      modified_content = template_content.replace("_worker_thread", f"{worker_thread}")

# Step 3: Write modified content to new or same file
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
     
     await context.add_init_script(path="./worker_intercept.js")
     page =await context.new_page()
    #  page.on("console", lambda msg: print("PAGE LOG:", msg.text))
  
  
     
     hardware_threads = await page.evaluate("() => navigator.hardwareConcurrency")
     print("Spoofed hardwareConcurrency:", hardware_threads)
     async def handle_worker(worker):
         print("worker created:", worker.url)
         
        #  def on_close():
         worker.on("close", lambda: print("worker destroyed: " + worker.url))
 
        #  worker.on("close", on_close)

    

    #  print(browser.contexts)
     page.set_default_timeout(0)
     await page.goto("https://www.delta.com/flightsearch/book-a-flight")
     from_ = [
    "SLC",  # Salt Lake City
    "LAX",  # Los Angeles
    "PDX",  # Portland
    "SEA",  # Seattle
    "PHX",  # Phoenix
    "SFO",  # San Francisco
    "AUS",  # Austin
    "IAH",  # Houston
    "DFW",  # Dallas/Fort Worth
    "BOI"   # Boise
]

     to = [
    "SAN",  # San Diego
    "EWR",  # Newark
    "ANC",  # Anchorage
    "LAS",  # Las Vegas
    "MSP",  # Minneapolis–St. Paul
    "ATL",  # Atlanta
    "DTW",  # Detroit
    "JFK",  # New York (JFK)
    "BOS",  # Boston
    "GDL"   # Guadalajara (Mexico)
]

    #  await page.locator('').click()
    #  test= t page.locator('#shopWithMiles').is_visible
    #  print(test)
     await page.locator("#onetrust-close-btn-container").click()
     await page.locator("#selectTripType-val").click(timeout=1000)
     await page.locator("#ui-list-selectTripType1").click()
     await page.locator('#shopWithMiles').click(force=True)
     await page.locator('#input_departureDate_1').click(timeout=3000)
     await page.locator(f"""[aria-label="27 June 2025, Friday"]""").click()
     await page.locator(f"""[aria-label="done"]""").click()




    #  print (test)
     for x in range(10):
        
        from_airport = await page.locator('#fromAirportName').click(force=True)
        print(from_airport)
        await page.locator("#search_input").fill("")
        searchInput=page.locator("#search_input")
        
        await searchInput.press_sequentially(f'{from_[x]}',delay=100)
        await page.wait_for_timeout(2000)
        await searchInput.press("Enter")

        to_airport= await page.locator('#toAirportName').click(force=True)
        
        await page.locator("#search_input").fill("")
        searchInput=page.locator("#search_input")
        await searchInput.press_sequentially(f'{to[x]}',delay=100)
        await page.wait_for_timeout(2000)
        await searchInput.press("Enter")
        
        await page.locator("#btnSubmit").click()
        # value= await departDate.input_value()
        # if(not value):
        #  await departDate.press_sequentially(f"{current_date_selectorUi}",delay=300)
    #  await   page.screenshot(path="partialbeforeSearch.png") 
        # await context.tracing.start(screenshots=True, snapshots=True, sources=True)
        # await page.locator("#findFlights").nth(0).click()
        await page.wait_for_load_state("domcontentloaded")
        await page.wait_for_timeout(10000)
        
       
        
        
        
        # await page.locator(f"""[class="flexible-dates-grid-row-cell isPriceCell cursorPointer isSelected isInRange ng-star-inserted"] [tabindex="0"]""").click(timeout=2000)
        await page.locator(f"""[class="idp-btn idp-primary idp-btn-large + ' ng-star-inserted"]""").click(timeout=2000)
        await page.wait_for_timeout(10000)
        await page.screenshot(path="partialbeforeSearch.png")
       
        moreResult = page.get_by_text(" See More Results ")
        test = await page.get_by_text(" See More Results ").is_visible()
        print(test)
        while(await moreResult.is_visible()):
            if await moreResult.is_visible(): 
             await moreResult.evaluate("el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })")
             await moreResult.click()
            #  await page.get_by_text(" See More Results ").click()
            moreResult = page.get_by_text(" See More Results ")
            print(moreResult)# Step 1: locator
            #  await scrollTillFeverResult.wait_for(timeout=5000)              # Step 2: wait for it
            #  await scrollTillFeverResult.evaluate("el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })")  # Step 3: scroll
        await page.goto("https://www.delta.com/flightsearch/book-a-flight")
    #  scroll_step = 200

# # Get the total scrollable height of the page
#      total_height = await page.evaluate("document.body.scrollHeight")

# # Calculate number of scrolls needed
#      num_steps = total_height // scroll_step


    #  for _ in range(num_steps):
    #   await page.mouse.wheel(0, scroll_step)
    #   await page.wait_for_timeout(800)  

    #  html = await page.content()
     

     await   page.screenshot(path="test.png")    
     await browser.close()

     with open("worker_intercept.js", "r", encoding="utf-8") as f:
      template_content = f.read()
      modified_content = template_content.replace(f"{worker_thread}", "_worker_thread")
      with open("worker_intercept.js", "w", encoding="utf-8") as f:
       f.write(modified_content)
       print("File updated successfully.")
 
asyncio.run(main())
