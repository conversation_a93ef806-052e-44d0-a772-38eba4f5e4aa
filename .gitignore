# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.venv/
.env/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# Distribution / packaging
build/
dist/
*.egg-info/
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.cache
nosetests.xml
coverage.xml
*.cover
.pytest_cache/

# PyCharm / VSCode / other IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Environment files
.env
.env.*

# mypy, pylint, etc.
.mypy_cache/
.dmypy.json
.pyre/
.pytype/

# Misc
.DS_Store
*.log

complete_anti_detection_guide.md