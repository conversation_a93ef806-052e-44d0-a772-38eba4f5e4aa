Object.defineProperty(navigator, "webdriver", { get: () => undefined });
window.chrome = { runtime: {} };
Object.defineProperty(navigator, "plugins", { get: () => [1, 2, 3] });
Object.defineProperty(navigator, "languages", { get: () => ["en-US", "en"] });
const originalQuery = window.navigator.permissions.query;
window.navigator.permissions.query = (parameters) =>
  parameters.name === "notifications"
    ? Promise.resolve({ state: Notification.permission })
    : originalQuery(parameters);
