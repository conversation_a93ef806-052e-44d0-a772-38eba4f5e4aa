import asyncio
import random
from datetime import datetime
from enhanced_headless_config import create_stealth_context

async def comprehensive_stealth_test():
    """
    Comprehensive example showing how to use enhanced headless configuration
    with all anti-detection features enabled.
    """
    
    # Configuration options
    proxy_server = "http://127.0.0.1:8080"  # Set to None if no proxy needed
    use_proxy = False  # Set to True to enable proxy
    
    print("🚀 Starting enhanced stealth browser test...")
    
    # Create stealth browser context
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,  # Set to False for debugging
        proxy_server=proxy_server if use_proxy else None
    )
    
    try:
        # Add enhanced stealth script for maximum protection
        await context.add_init_script(path="./enhanced_stealth_script.js")
        print("✅ Enhanced stealth script loaded")
        
        # Create new page
        page = await context.new_page()
        
        # Set additional page-level configurations
        await page.set_extra_http_headers({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        # Set viewport to realistic size
        await page.set_viewport_size({"width": 1366, "height": 768})
        
        print("🔧 Page configuration completed")
        
        # Test 1: Headless detection test
        print("\n📋 Test 1: Headless Detection Test")
        await test_headless_detection(page, config)
        
        # Test 2: Bot detection test  
        print("\n📋 Test 2: Bot Detection Test")
        await test_bot_detection(page, config)
        
        # Test 3: Real website test (example with airline site)
        print("\n📋 Test 3: Real Website Test")
        await test_real_website(page, config)
        
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        
    finally:
        print("\n🧹 Cleaning up...")
        await browser.close()
        await playwright_instance.stop()
        print("✅ Browser closed successfully")

async def test_headless_detection(page, config):
    """Test against headless detection sites"""
    
    # Add human-like delay before navigation
    await config.add_random_delays(page, 1000, 3000)
    
    print("   🌐 Navigating to headless detection site...")
    await page.goto("https://infosimples.github.io/detect-headless/")
    
    # Wait for page to load
    await page.wait_for_load_state("domcontentloaded")
    await config.add_random_delays(page, 2000, 4000)
    
    # Simulate human behavior
    await config.simulate_human_behavior(page)
    
    # Take screenshot for verification
    await page.screenshot(path="headless_detection_test.png")
    print("   📸 Screenshot saved: headless_detection_test.png")
    
    # Check for detection indicators
    try:
        # Look for common detection indicators
        webdriver_detected = await page.evaluate("() => navigator.webdriver")
        chrome_present = await page.evaluate("() => !!window.chrome")
        plugins_count = await page.evaluate("() => navigator.plugins.length")
        
        print(f"   📊 Detection Results:")
        print(f"      - WebDriver detected: {webdriver_detected}")
        print(f"      - Chrome object present: {chrome_present}")
        print(f"      - Plugins count: {plugins_count}")
        
    except Exception as e:
        print(f"   ⚠️ Could not check detection indicators: {e}")

async def test_bot_detection(page, config):
    """Test against bot detection"""
    
    await config.add_random_delays(page, 1000, 2000)
    
    print("   🌐 Navigating to bot detection site...")
    await page.goto("https://bot.sannysoft.com/")
    
    await page.wait_for_load_state("domcontentloaded")
    await config.add_random_delays(page, 3000, 5000)
    
    # Simulate more complex human behavior
    await config.simulate_human_behavior(page)
    
    # Additional human-like actions
    await page.mouse.move(random.randint(200, 600), random.randint(200, 400))
    await config.add_random_delays(page, 500, 1500)
    
    # Scroll randomly
    for _ in range(3):
        await page.evaluate(f"window.scrollBy(0, {random.randint(100, 300)})")
        await config.add_random_delays(page, 800, 1500)
    
    await page.screenshot(path="bot_detection_test.png")
    print("   📸 Screenshot saved: bot_detection_test.png")

async def test_real_website(page, config):
    """Test with a real website (example)"""
    
    await config.add_random_delays(page, 1500, 3000)
    
    print("   🌐 Testing with real website...")
    
    try:
        # Example: Navigate to a flight booking site
        await page.goto("https://www.google.com/flights")
        await page.wait_for_load_state("domcontentloaded")
        
        # Wait for any dynamic content to load
        await config.add_random_delays(page, 3000, 6000)
        
        # Simulate human interaction
        await config.simulate_human_behavior(page)
        
        # Try to interact with search elements (if present)
        try:
            # Look for search inputs
            search_inputs = await page.query_selector_all('input[type="text"]')
            if search_inputs:
                print(f"   ✅ Found {len(search_inputs)} search inputs")
                
                # Click on first input to simulate user interaction
                await search_inputs[0].click()
                await config.add_random_delays(page, 500, 1000)
                
        except Exception as e:
            print(f"   ℹ️ Could not interact with search elements: {e}")
        
        await page.screenshot(path="real_website_test.png")
        print("   📸 Screenshot saved: real_website_test.png")
        print("   ✅ Real website test completed")
        
    except Exception as e:
        print(f"   ⚠️ Real website test failed: {e}")

async def flight_scraper_example():
    """
    Example of how to use enhanced stealth for flight scraping
    """
    print("✈️ Starting flight scraper with enhanced stealth...")
    
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,
        proxy_server=None
    )
    
    try:
        await context.add_init_script(path="./enhanced_stealth_script.js")
        page = await context.new_page()
        
        # Set realistic headers for flight booking
        await page.set_extra_http_headers({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://www.google.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site'
        })
        
        # Example flight search URL (replace with actual airline URL)
        flight_url = "https://www.example-airline.com/flights"
        
        print(f"🌐 Navigating to: {flight_url}")
        await page.goto(flight_url)
        await page.wait_for_load_state("domcontentloaded")
        
        # Human-like behavior before interacting
        await config.simulate_human_behavior(page)
        await config.add_random_delays(page, 2000, 4000)
        
        # Your scraping logic would go here
        print("🔍 Scraping logic would be implemented here...")
        
        await page.screenshot(path="flight_scraper_test.png")
        print("📸 Flight scraper screenshot saved")
        
    finally:
        await browser.close()
        await playwright_instance.stop()

if __name__ == "__main__":
    print("🎭 Enhanced Headless Browser Anti-Detection Demo")
    print("=" * 50)
    
    # Run comprehensive test
    asyncio.run(comprehensive_stealth_test())
    
    print("\n" + "=" * 50)
    print("🎯 All tests completed!")
    print("\nTo use this in your scrapers:")
    print("1. Import: from enhanced_headless_config import create_stealth_context")
    print("2. Use: browser, context, playwright, config = await create_stealth_context(headless=True)")
    print("3. Add: await context.add_init_script(path='./enhanced_stealth_script.js')")
    print("4. Simulate human behavior with: await config.simulate_human_behavior(page)")
    print("5. Add delays with: await config.add_random_delays(page, min_ms, max_ms)")
