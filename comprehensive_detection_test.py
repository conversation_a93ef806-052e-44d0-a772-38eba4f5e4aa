import asyncio
import json
from datetime import datetime
from enhanced_headless_config import create_stealth_context

async def test_detection_sites():
    """
    Comprehensive test against multiple detection sites including CreepJS
    """
    
    print("🚀 Starting comprehensive detection tests...")
    print("=" * 60)
    
    # Test sites to check
    test_sites = [
        {
            "name": "CreepJS",
            "url": "https://abrahamjuliot.github.io/creepjs/",
            "script": "./creepjs_bypass.js",
            "wait_time": 10000,
            "success_indicators": ["fingerprint", "score"],
            "failure_indicators": ["400", "access denied", "blocked"]
        },
        {
            "name": "Headless Detection",
            "url": "https://infosimples.github.io/detect-headless/",
            "script": "./creepjs_bypass.js",
            "wait_time": 5000,
            "success_indicators": ["not headless", "passed"],
            "failure_indicators": ["headless", "detected", "failed"]
        },
        {
            "name": "Bot Detection",
            "url": "https://bot.sannysoft.com/",
            "script": "./creepjs_bypass.js", 
            "wait_time": 8000,
            "success_indicators": ["human", "passed"],
            "failure_indicators": ["bot", "automation", "detected"]
        }
    ]
    
    results = {}
    
    for site in test_sites:
        print(f"\n🧪 Testing {site['name']}...")
        print("-" * 40)
        
        result = await test_single_site(site)
        results[site['name']] = result
        
        # Print result
        if result['success']:
            print(f"✅ {site['name']}: PASSED")
        else:
            print(f"❌ {site['name']}: FAILED - {result['reason']}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    for name, result in results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{name:20} | {status}")
        if not result['success']:
            print(f"{'':20} | Reason: {result['reason']}")
    
    print(f"\nOverall Score: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your stealth configuration is working well.")
    elif passed >= total * 0.7:
        print("⚠️ Most tests passed. Some fine-tuning may be needed.")
    else:
        print("🔧 Multiple failures detected. Configuration needs improvement.")
    
    return results

async def test_single_site(site_config):
    """Test a single detection site"""
    
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,
        proxy_server=None
    )
    
    try:
        # Add the specified bypass script
        await context.add_init_script(path=site_config['script'])
        
        page = await context.new_page()
        
        # Setup page for API access
        await config.setup_page_for_api_access(page)
        
        # Add initial delay
        await config.add_random_delays(page, 1000, 3000)
        
        print(f"   🌐 Navigating to {site_config['url']}")
        
        # Navigate to site
        await page.goto(site_config['url'])
        await page.wait_for_load_state("domcontentloaded")
        
        # Wait for site to load and execute scripts
        await config.add_random_delays(page, site_config['wait_time'], site_config['wait_time'] + 2000)
        
        # Simulate human behavior
        await config.simulate_human_behavior(page)
        
        # Additional wait for dynamic content
        await config.add_random_delays(page, 2000, 4000)
        
        # Get page content
        content = await page.content()
        content_lower = content.lower()
        
        # Take screenshot
        screenshot_name = f"{site_config['name'].lower().replace(' ', '_')}_test.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"   📸 Screenshot saved: {screenshot_name}")
        
        # Analyze results
        success = True
        reason = "Test completed successfully"
        
        # Check for failure indicators
        for indicator in site_config['failure_indicators']:
            if indicator.lower() in content_lower:
                success = False
                reason = f"Detected failure indicator: '{indicator}'"
                break
        
        # If no failures found, check for success indicators
        if success:
            found_success = False
            for indicator in site_config['success_indicators']:
                if indicator.lower() in content_lower:
                    found_success = True
                    break
            
            if not found_success and site_config['success_indicators']:
                success = False
                reason = "No success indicators found"
        
        # Special handling for CreepJS API errors
        if site_config['name'] == "CreepJS":
            if "400" in content or "access denied" in content_lower:
                success = False
                reason = "API access denied (400 error)"
            elif "fingerprint" in content_lower and "score" in content_lower:
                success = True
                reason = "Fingerprint analysis completed"
        
        return {
            'success': success,
            'reason': reason,
            'content_length': len(content),
            'screenshot': screenshot_name
        }
        
    except Exception as e:
        print(f"   ❌ Error testing {site_config['name']}: {e}")
        return {
            'success': False,
            'reason': f"Exception occurred: {str(e)}",
            'content_length': 0,
            'screenshot': None
        }
        
    finally:
        await browser.close()
        await playwright_instance.stop()

async def test_creepjs_specifically():
    """
    Specific test for CreepJS with detailed analysis
    """
    
    print("🎯 Running detailed CreepJS test...")
    
    browser, context, playwright_instance, config = await create_stealth_context(
        headless=True,
        proxy_server=None
    )
    
    try:
        # Add CreepJS bypass script
        await context.add_init_script(path="./creepjs_bypass.js")
        
        page = await context.new_page()
        
        # Setup for API access
        await config.setup_page_for_api_access(page)
        
        # Navigate to CreepJS
        print("   🌐 Loading CreepJS...")
        await page.goto("https://abrahamjuliot.github.io/creepjs/")
        await page.wait_for_load_state("domcontentloaded")
        
        # Wait for analysis to complete
        print("   ⏳ Waiting for fingerprint analysis...")
        await config.add_random_delays(page, 15000, 20000)
        
        # Check for specific elements
        try:
            # Look for fingerprint score
            score_element = await page.query_selector('[data-fingerprint-score]')
            if score_element:
                score = await score_element.get_attribute('data-fingerprint-score')
                print(f"   📊 Fingerprint score: {score}")
            
            # Look for trust score
            trust_element = await page.query_selector('[data-trust-score]')
            if trust_element:
                trust = await trust_element.get_attribute('data-trust-score')
                print(f"   🛡️ Trust score: {trust}")
                
        except Exception as e:
            print(f"   ℹ️ Could not extract scores: {e}")
        
        # Take detailed screenshot
        await page.screenshot(path="creepjs_detailed.png", full_page=True)
        print("   📸 Detailed screenshot saved: creepjs_detailed.png")
        
        # Get and analyze content
        content = await page.content()
        
        if "400" in content:
            print("   ❌ API access denied detected")
            return False
        elif "fingerprint" in content.lower():
            print("   ✅ Fingerprint analysis completed")
            return True
        else:
            print("   ⚠️ Unclear result - check screenshot")
            return None
            
    finally:
        await browser.close()
        await playwright_instance.stop()

if __name__ == "__main__":
    print("🎭 Advanced Detection Testing Suite")
    print("=" * 60)
    
    # Run comprehensive tests
    asyncio.run(test_detection_sites())
    
    print("\n" + "=" * 60)
    print("🎯 Running detailed CreepJS analysis...")
    
    # Run specific CreepJS test
    creepjs_result = asyncio.run(test_creepjs_specifically())
    
    print("\n" + "=" * 60)
    print("📋 RECOMMENDATIONS:")
    
    if creepjs_result is True:
        print("✅ CreepJS test passed - your configuration is working well!")
        print("💡 You can now use this setup for your scraping projects.")
    elif creepjs_result is False:
        print("❌ CreepJS test failed - API access issues detected.")
        print("💡 Try using a different IP address or proxy.")
        print("💡 Consider adding more delays between requests.")
    else:
        print("⚠️ CreepJS test inconclusive - manual review needed.")
        print("💡 Check the screenshots for detailed analysis.")
    
    print("\n🔧 Next steps:")
    print("1. Review all screenshots generated")
    print("2. If tests fail, try running with headless=False first")
    print("3. Consider using residential proxies for better results")
    print("4. Adjust delay timings based on your target websites")
